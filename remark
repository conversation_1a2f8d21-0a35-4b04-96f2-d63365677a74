
* Aliyun stream recognition

#+BEGIN_SRC java
public String streamRecognition(Sinks.Many<byte[]> audioSink) {
        // 获取当前的recognizer实例
        Recognition currentRecognizer;
        synchronized (recognizerLock) {
            currentRecognizer = this.recognizer;
        }

        // 创建识别参数
        RecognitionParam param = RecognitionParam.builder()
                .model(DEFAULT_MODEL)
                .format("pcm")
                .sampleRate(AudioUtils.SAMPLE_RATE) // 使用16000Hz采样率
                .apiKey(apiKey)
                .build();

        // 使用 Reactor 执行流式识别
        var recognitionMono = Mono.<String>create(sink -> {

            try {
                // 执行流式识别
                currentRecognizer.streamCall(param, Flowable.create(emitter -> {
                            audioSink.asFlux().subscribe(
                                    chunk -> {
                                        logger.info("receive new chunk {}", chunk.length);
                                        emitter.onNext(ByteBuffer.wrap(chunk));
                                    },
                                    err -> {
                                        err.printStackTrace();
                                        emitter.onError(err);
                                    },
                                    () -> {
                                        logger.info("audio complete");
                                        emitter.onComplete();
                                    }
                            );
                        }, BackpressureStrategy.BUFFER))
                        .doOnError(err -> {
                            err.printStackTrace();
                        })
                        .forEach(result -> {
                            if (result.isSentenceEnd()) {
                                System.out.println("Fix:" + result.getSentence().getText());
                            } else {
                                System.out.println("Result:" + result.getSentence().getText());
                                sink.success(result.getSentence().getText());
                            }
                        });

            } catch (Exception e) {
                e.printStackTrace();
            }
        });

        // 设置最大等待时间，防止识别过程无限阻塞
        return recognitionMono
                .doOnNext(result -> logger.info("语音识别结果: {}", result))
                .doOnError(error -> {
                    logger.error("语音识别失败", error);
                })
                .block();
    }

#+END_SRC

* Local ota url

#+BEGIN_SRC sh
http://192.168.1.184:8091/api/v1/ota
https://api.tenclass.net/xiaozhi/ota/
#+END_SRC

* TODO [0/5]
- [ ] MCP
- [ ] 主动唤醒
- [-] 声纹识别
- [ ] 主动场景切换
- [ ] 意图识别
