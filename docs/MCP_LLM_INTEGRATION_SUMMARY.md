# MCP工具与LLM集成总结

## 问题发现

用户指出了一个关键问题：**MCP工具只是被注册了，但LLM好像没有用到**。

经过分析，我发现了以下问题：

### 1. 唤醒词处理禁用了工具调用
在`DialogueService.handleWakeWord()`方法中：
```java
// 原来的代码 - 禁用工具调用
handleText(session, text, false);
```

这意味着当用户说唤醒词时，LLM无法使用任何工具，包括MCP工具。

### 2. 缺乏工具使用验证机制
系统没有验证MCP工具是否被正确注册和可用的机制，难以发现和调试工具相关问题。

## 解决方案

### 1. 修复唤醒词工具调用问题 ✅

**修改位置**: `src/main/java/com/xiaozhi/dialogue/service/DialogueService.java:548`

```java
// 修复后的代码 - 启用工具调用
handleText(session, text, true);
```

**影响**: 现在所有对话场景都启用工具调用：
- ✅ **语音输入**: `handleText(session, finalText, true)` - 启用工具调用
- ✅ **唤醒词**: `handleText(session, text, true)` - **已修复，启用工具调用**
- ✅ **文本输入**: `handleText(chatSession, message.getText(), true)` - 启用工具调用

### 2. 创建MCP工具使用验证器 ✅

**新增文件**: `src/main/java/com/xiaozhi/dialogue/llm/tool/mcp/McpToolUsageVerifier.java`

**功能特性**:
- **工具注册验证**: 验证MCP工具是否被正确注册到会话中
- **可用性检查**: 检查MCP工具是否可被LLM使用
- **详细调试信息**: 输出所有工具的详细信息用于调试
- **统计报告**: 提供工具数量和类型的统计信息

**核心方法**:
```java
// 验证MCP工具注册情况
public McpToolVerificationResult verifyMcpToolsRegistration(ChatSession chatSession)

// 验证MCP工具是否可被LLM使用
public boolean verifyMcpToolsAvailableForLLM(ChatSession chatSession)

// 输出详细调试信息
public void debugToolInformation(ChatSession chatSession)
```

### 3. 集成验证机制到连接流程 ✅

**修改位置**: `src/main/java/com/xiaozhi/communication/common/MessageHandler.java`

在`afterConnection`方法中添加了MCP工具验证：
```java
// 验证MCP工具注册情况
Thread.startVirtualThread(() -> {
    try {
        Thread.sleep(1000); // 等待异步初始化完成
        var verificationResult = mcpToolUsageVerifier.verifyMcpToolsRegistration(chatSession);
        logger.info("MCP工具验证结果: {}", verificationResult);
        
        // 输出详细调试信息
        if (logger.isDebugEnabled()) {
            mcpToolUsageVerifier.debugToolInformation(chatSession);
        }
    } catch (Exception e) {
        logger.error("MCP工具验证失败 - SessionId: {}", sessionId, e);
    }
});
```

## 工具调用流程验证

### 1. LLM工具调用链路
```
用户输入 → DialogueService.handleText(useTool=true) 
        → ChatService.chatStreamBySentence(useFunctionCall=true)
        → ChatService.chatStream(useToolCall=true)
        → ToolCallingChatOptions.builder().toolCallbacks(session.getToolCallbacks())
        → LLM模型接收工具列表并可调用
```

### 2. MCP工具注册链路
```
会话建立 → MessageHandler.afterConnection()
        → McpToolRegistrationService.initializeAllMcpTools()
        → 并行初始化设备端MCP和第三方MCP
        → 工具注册到session.getToolsSessionHolder()
        → session.getToolCallbacks()返回包含MCP工具的列表
```

### 3. 验证链路
```
MCP工具初始化完成 → McpToolUsageVerifier.verifyMcpToolsRegistration()
                  → 检查session.getToolCallbacks()中的MCP工具
                  → 输出验证结果和调试信息
```

## 日志输出示例

### 成功场景
```
2025-07-24 11:20:00.123 INFO  [McpToolRegistrationService] 开始初始化会话 session_456 的MCP工具
2025-07-24 11:20:00.150 INFO  [ThirdPartyMcpManager] 成功连接到MCP端点: https://filesystem-mcp.example.com/api/v1/mcp, 获取到 3 个工具
2025-07-24 11:20:00.175 INFO  [McpToolRegistrationService] 成功注册 3 个第三方MCP工具到会话 session_456
2025-07-24 11:20:01.200 INFO  [McpToolUsageVerifier] MCP工具验证完成 - SessionId: session_456, 总工具数: 8, MCP工具数: 3, 第三方MCP工具数: 3
2025-07-24 11:20:01.201 INFO  [McpToolUsageVerifier] 已注册的MCP工具: mcp_read_file, mcp_write_file, mcp_list_directory
```

### 调试信息
```
2025-07-24 11:20:01.202 INFO  [McpToolUsageVerifier] === 调试工具信息 - SessionId: session_456 ===
2025-07-24 11:20:01.203 INFO  [McpToolUsageVerifier] 总工具数: 8
2025-07-24 11:20:01.204 INFO  [McpToolUsageVerifier] MCP工具 (3个):
2025-07-24 11:20:01.205 INFO  [McpToolUsageVerifier]   - mcp_read_file: 读取文件内容
2025-07-24 11:20:01.206 INFO  [McpToolUsageVerifier]   - mcp_write_file: 写入文件内容
2025-07-24 11:20:01.207 INFO  [McpToolUsageVerifier]   - mcp_list_directory: 列出目录内容
2025-07-24 11:20:01.208 INFO  [McpToolUsageVerifier] 功能工具 (5个):
2025-07-24 11:20:01.209 INFO  [McpToolUsageVerifier]   - func_playMusic: 音乐播放助手
2025-07-24 11:20:01.210 INFO  [McpToolUsageVerifier]   - func_playHuiBen: 绘本播放助手
```

## 技术改进

### 1. 连接状态管理优化
修复了之前`activeClients.put(endpointUrl, null)`的不合理设计：
- **原来**: 存储null值作为占位符
- **现在**: 使用`ConnectionInfo`存储有用的连接信息

### 2. 工具可用性保证
- **全场景启用**: 所有对话场景都启用工具调用
- **实时验证**: 连接建立后自动验证工具注册情况
- **调试支持**: 提供详细的调试信息

### 3. 错误处理改进
- **异步验证**: 不阻塞主连接流程
- **异常隔离**: 验证失败不影响正常功能
- **详细日志**: 便于问题排查和调试

## 使用场景验证

### 1. 用户语音对话
```
用户: "小智，帮我读取/home/<USER>/document.txt文件"
系统: 调用 mcp_read_file 工具 → 返回文件内容 → LLM处理并回复
```

### 2. 用户唤醒词
```
用户: "小智" (唤醒词)
系统: 现在也可以使用MCP工具了！（之前被禁用）
```

### 3. 文本输入
```
用户通过WebSocket发送文本消息
系统: 正常使用MCP工具处理请求
```

## 监控和维护

### 1. 日志监控
- 关注MCP工具验证结果日志
- 监控工具注册成功/失败统计
- 检查工具调用频率和成功率

### 2. 调试支持
- 启用DEBUG日志级别查看详细工具信息
- 使用验证器输出的调试信息排查问题
- 监控工具数量变化

### 3. 性能监控
- 监控工具初始化时间
- 检查验证过程的性能影响
- 关注异步处理的资源使用

## 总结

通过这次改进，我们解决了MCP工具"只注册不使用"的问题：

1. ✅ **修复了唤醒词场景的工具调用禁用问题**
2. ✅ **创建了完整的工具使用验证机制**
3. ✅ **优化了连接状态管理**
4. ✅ **提供了详细的调试和监控支持**

现在MCP工具可以在所有对话场景中被LLM正常使用，并且系统具备了完善的验证和调试能力。
