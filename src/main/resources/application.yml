logging:
  level:
    root: INFO
    com.xiaozhi: INFO
    org.springframework: INFO
    io.github.imfangs.dify.client.impl.StreamEventDispatcher: ERROR

server:
  port: 8091
  servlet:
    context-path: /api/v1
    forward-headers-strategy: framework

mybatis-plus:
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
    default-enum-type-handler: org.apache.ibatis.type.EnumOrdinalTypeHandler
    map-underscore-to-camel-case: true
  global-config:
    db-config:
      id-type: auto
      logic-delete-field: isDeleted
      logic-delete-value: 1
      logic-not-delete-value: 0
  mapper-locations: classpath:/mapper/**/*.xml
  # type-handlers-package: cn.lideyw.adverkit.core.model

# 数据库连接池通用配置
spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    password: qweQWE!@#
    url: ***************************************************************************************************************
    username: root
    hikari:
      maximum-pool-size: 100
      minimum-idle: 5

  # Redis 配置
  data:
    redis:
      host: localhost
      port: 6379
      password:
      database: 0
      timeout: 5000ms
      lettuce:
        pool:
          max-active: 8
          max-wait: -1ms
          max-idle: 8
          min-idle: 0

  servlet:
    multipart:
      max-file-size: 2458624KB
      max-request-size: 2458624KB
  jackson:
    property-naming-strategy: SNAKE_CASE
    # Date format string or a fully-qualified date format class name. For instance, 'yyyy-MM-dd HH:mm:ss'
    date-format: "yyyy-MM-dd'T'hh:mm:ss'Z'"
    # Locale used for formatting
    time-zone: "GMT+8"
  jmx:
    enabled: false
  websocket:
    allowed-origins: "*"
  threads:
    virtual:
      enabled: true
  profiles:
    active: dev

mqtt:
  broker:
    url: tcp://localhost:1883
    username: ""
    password: ""
    connection-timeout: 30
    keep-alive-interval: 60
    automatic-reconnect: true
    clean-session: true
  client:
    id: xiaozhi-server
    random-suffix: true
  topic:
    prefix: xiaozhi
    qos: 1
  session:
    timeout: 300
    heartbeat-interval: 60
    cleanup-interval: 30
  # UDP服务器配置
  udp:
    port: 8884
    host: 0.0.0.0
    receive-buffer-size: 65536
    send-buffer-size: 65536
    worker-threads: 4
    session-timeout-ms: 300000
  # 加密配置
  encryption:
    algorithm: AES-128-CTR
    key-length: 16
    nonce-length: 16
    sequence-window-size: 1000

# 小智应用配置
xiaozhi:
  upload:
    path: uploads
  # 服务器地址配置
  server:
    address:
      # host: "*************"  # 可选：手动指定服务器IP，不配置则自动检测
      # port: 8091             # 可选：手动指定端口，不配置则使用 server.port
      ssl: false               # 是否启用 HTTPS
    websocket:
      path: "/ws/xiaozhi/v1/" # 可选：自定义WebSocket路径
      ssl: false               # 是否启用 WSS
    mqtt:
      # host:
      port: 1883
      client-publish-topic: device-server
      server-subscribe-topic: device-server-enhance
  # 激活码配置
  activation-code:
    storage-type: mysql        # 存储类型：mysql 或 redis
    validity-hours: 24         # 激活码有效期（小时）
    code-length: 6             # 激活码长度
    code-prefix: ""            # 激活码前缀
    redis:
      key-prefix: "xiaozhi:activation:"  # Redis key 前缀
      cluster-enabled: false   # 是否启用 Redis 集群模式
  mcp:
    device:
      max-tools-count: 32
      timeout-ms: 30000
      auto-discovery: true
    connection:
      timeout-ms: 10000
      retry-count: 3
      retry-interval-ms: 1000
    thirdparty:
      enabled: true
      endpoints: []

# VAD 配置
vad:
  prebuffer-ms: 200
  energy-threshold: 0.5
  speech-threshold: 0.5
  silence-threshold: 0.5
  silence-ms: 1000
  model:
    path: models/silero_vad.onnx
    type: silero
    sample-rate: 16000
    window-size: 512

# 邮件配置
email:
  smtp:
    username: ""
    password: ""

jwt:
  secret: MGExZjM4ZDhmM2U3NzU4Yjk5NGE2MDQxMTk2OTM3ZTYK