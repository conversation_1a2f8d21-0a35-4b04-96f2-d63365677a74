package com.xiaozhi.dao;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xiaozhi.entity.SysConfig;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

/**
 * 模型 数据层
 * 
 * <AUTHOR>
 * 
 */
@Mapper
public interface ConfigMapper extends BaseMapper<SysConfig> {
    int add(SysConfig config);

    int update(SysConfig config);

    List<SysConfig> query(SysConfig config);

    @Update("UPDATE sys_config SET is_default = 0 WHERE user_id = #{userId} AND type = #{type}")
    int resetDefault(@Param("userId") Integer userId, @Param("type") String type);
}
