package com.xiaozhi.dao;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xiaozhi.entity.SysRole;
import org.apache.ibatis.annotations.Mapper;

/**
 * 角色管理 数据层
 * 
 * <AUTHOR>
 * 
 */
@Mapper
public interface RoleMapper extends BaseMapper<SysRole> {
  List<SysRole> query(SysRole role);

  int update(SysRole role);

  int resetDefault(SysRole role);

  int add(SysRole role);
}