package com.xiaozhi.dao;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xiaozhi.entity.SysMessage;
import org.apache.ibatis.annotations.Mapper;

/**
 * 聊天记录 数据层
 * 
 * <AUTHOR>
 * 
 */
@Mapper
public interface MessageMapper extends BaseMapper<SysMessage> {

  int add(SysMessage message);

  int delete(SysMessage message);

  List<SysMessage> query(SysMessage message);
}