package com.xiaozhi.controller;

import com.xiaozhi.common.web.BizError;
import com.xiaozhi.common.web.Resp;
import com.xiaozhi.service.SysUserService;
import com.xiaozhi.vo.EmailCaptchaParams;
import com.xiaozhi.vo.UserCreateParams;
import com.xiaozhi.vo.UserQueryParams;
import com.xiaozhi.vo.UserUpdateParams;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.vavr.control.Either;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.*;

/**
 * 用户管理
 *
 * @author: Joey
 *
 */
@RestController
@RequestMapping("/users")
@Tag(name = "用户管理", description = "用户管理")
public class UserController extends BaseController {

    @Resource
    private SysUserService userService;

    @GetMapping()
    @Operation(summary = "用户列表")
    public Resp find(UserQueryParams params) {
        return userService.findPage(params);
    }

    @PostMapping()
    @Operation(summary = "添加用户")
    public Either<BizError, ?> add(@RequestBody UserCreateParams params) {
        return userService.add(params);
    }

    @PutMapping("/{id}")
    @Operation(summary = "更新用户")
    public Either<BizError, ?> update(@PathVariable Integer id, @RequestBody UserUpdateParams params) {
        return userService.update(id, params);
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "删除用户")
    public Either<BizError, ?> delete(@PathVariable Integer id) {
        return userService.delete(id);
    }

    @PostMapping("/email-captcha")
    @Operation(summary = "发送邮箱验证码")
    public Either<BizError, ?> sendEmailCaptcha(@RequestBody EmailCaptchaParams params) {
        return userService.sendEmailCaptcha(params);
    }

    @GetMapping("/check-captcha")
    @Operation(summary = "验证验证码")
    public Either<BizError, ?> checkCaptcha(@RequestParam String code, @RequestParam String email) {
        return userService.checkCaptcha(code, email);
    }

    @GetMapping("/check-user")
    @Operation(summary = "检查用户名和邮箱是否已存在")
    public Either<BizError, ?> checkUser(@RequestParam String username, @RequestParam String email) {
        return userService.checkUser(username, email);
    }

}