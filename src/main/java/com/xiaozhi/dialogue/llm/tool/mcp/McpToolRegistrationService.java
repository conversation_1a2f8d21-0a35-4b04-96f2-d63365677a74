package com.xiaozhi.dialogue.llm.tool.mcp;

import com.xiaozhi.communication.common.ChatSession;
import com.xiaozhi.dialogue.llm.tool.mcp.device.DeviceMcpService;
import com.xiaozhi.dialogue.llm.tool.mcp.thirdparty.ThirdPartyMcpManager;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.ai.tool.function.FunctionToolCallback;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * MCP工具注册服务
 * 统一管理设备端MCP和第三方MCP的工具注册
 */
@Service
public class McpToolRegistrationService {
    private static final Logger logger = LoggerFactory.getLogger(McpToolRegistrationService.class);
    
    @Resource
    private DeviceMcpService deviceMcpService;
    
    @Resource
    private ThirdPartyMcpManager thirdPartyMcpManager;
    
    /**
     * 初始化会话的所有MCP工具
     * 包括设备端MCP和第三方MCP工具
     *
     * @param chatSession 聊天会话
     */
    public void initializeAllMcpTools(ChatSession chatSession) {
        logger.info("开始初始化会话 {} 的MCP工具", chatSession.getSessionId());
        
        // 异步初始化设备端MCP工具
//        CompletableFuture<Void> deviceMcpFuture = CompletableFuture.runAsync(() -> {
//            try {
//                logger.debug("初始化设备端MCP工具 - SessionId: {}", chatSession.getSessionId());
//                deviceMcpService.initialize(chatSession);
//                logger.debug("设备端MCP工具初始化完成 - SessionId: {}", chatSession.getSessionId());
//            } catch (Exception e) {
//                logger.error("设备端MCP工具初始化失败 - SessionId: {}", chatSession.getSessionId(), e);
//            }
//        });
        
        // 异步初始化第三方MCP工具
        CompletableFuture<Void> thirdPartyMcpFuture = CompletableFuture.runAsync(() -> {
            try {
                logger.debug("初始化第三方MCP工具 - SessionId: {}", chatSession.getSessionId());
                initializeThirdPartyMcpTools(chatSession);
                logger.debug("第三方MCP工具初始化完成 - SessionId: {}", chatSession.getSessionId());
            } catch (Exception e) {
                logger.error("第三方MCP工具初始化失败 - SessionId: {}", chatSession.getSessionId(), e);
            }
        });
        
        // 等待两个初始化过程完成（可选，根据需求决定是否阻塞）
        try {
            CompletableFuture.allOf(thirdPartyMcpFuture).get();
            logger.info("会话 {} 的所有MCP工具初始化完成", chatSession.getSessionId());
        } catch (Exception e) {
            logger.error("等待MCP工具初始化完成时发生错误 - SessionId: {}", chatSession.getSessionId(), e);
        }
    }
    
    /**
     * 初始化第三方MCP工具
     * 从数据库中获取启用的端点并注册工具到会话中
     *
     * @param chatSession 聊天会话
     */
    private void initializeThirdPartyMcpTools(ChatSession chatSession) {
        try {
            // 确保第三方MCP端点已初始化
            int connectedCount = thirdPartyMcpManager.initializeEnabledEndpoints();
            logger.debug("第三方MCP端点连接数: {} - SessionId: {}", connectedCount, chatSession.getSessionId());
            
            // 获取所有第三方MCP工具
            List<FunctionToolCallback> thirdPartyTools = thirdPartyMcpManager.getAllTools();
            
            if (!thirdPartyTools.isEmpty()) {
                // 注册第三方MCP工具到会话
                for (FunctionToolCallback tool : thirdPartyTools) {
                    chatSession.getToolsSessionHolder().registerFunction(tool.getToolDefinition().name(), tool);
                    logger.debug("注册第三方MCP工具: {} - SessionId: {}", tool.getToolDefinition().name(), chatSession.getSessionId());
                }
                
                logger.info("成功注册 {} 个第三方MCP工具到会话 {}", thirdPartyTools.size(), chatSession.getSessionId());
            } else {
                logger.debug("没有可用的第三方MCP工具 - SessionId: {}", chatSession.getSessionId());
            }
            
        } catch (Exception e) {
            logger.error("初始化第三方MCP工具失败 - SessionId: {}", chatSession.getSessionId(), e);
        }
    }
    
    /**
     * 仅初始化设备端MCP工具
     *
     * @param chatSession 聊天会话
     */
    public void initializeDeviceMcpTools(ChatSession chatSession) {
        logger.debug("仅初始化设备端MCP工具 - SessionId: {}", chatSession.getSessionId());
        try {
            deviceMcpService.initialize(chatSession);
            logger.info("设备端MCP工具初始化完成 - SessionId: {}", chatSession.getSessionId());
        } catch (Exception e) {
            logger.error("设备端MCP工具初始化失败 - SessionId: {}", chatSession.getSessionId(), e);
        }
    }
    
    /**
     * 仅初始化第三方MCP工具
     *
     * @param chatSession 聊天会话
     */
    public void initializeThirdPartyMcpToolsOnly(ChatSession chatSession) {
        logger.debug("仅初始化第三方MCP工具 - SessionId: {}", chatSession.getSessionId());
        initializeThirdPartyMcpTools(chatSession);
    }
    
    /**
     * 重新加载第三方MCP工具
     * 清理现有连接并重新初始化
     *
     * @param chatSession 聊天会话
     */
    public void reloadThirdPartyMcpTools(ChatSession chatSession) {
        logger.info("重新加载第三方MCP工具 - SessionId: {}", chatSession.getSessionId());
        
        try {
            // 重新加载第三方MCP端点
            int connectedCount = thirdPartyMcpManager.reloadEndpoints();
            logger.debug("重新连接的第三方MCP端点数: {} - SessionId: {}", connectedCount, chatSession.getSessionId());
            
            // 重新注册工具到会话
            initializeThirdPartyMcpTools(chatSession);
            
        } catch (Exception e) {
            logger.error("重新加载第三方MCP工具失败 - SessionId: {}", chatSession.getSessionId(), e);
        }
    }
    
    /**
     * 获取会话中已注册的MCP工具数量统计
     *
     * @param chatSession 聊天会话
     * @return 工具数量统计信息
     */
    public McpToolStats getMcpToolStats(ChatSession chatSession) {
        int totalTools = chatSession.getToolCallbacks().size();
        
        // 统计MCP工具（名称以"mcp_"开头的工具）
        long mcpToolCount = chatSession.getToolCallbacks().stream()
                .filter(tool -> tool.getToolDefinition().name().startsWith("mcp_"))
                .count();
        
        // 获取第三方MCP工具数量
        int thirdPartyMcpToolCount = thirdPartyMcpManager.getAllTools().size();
        
        return new McpToolStats(totalTools, (int) mcpToolCount, thirdPartyMcpToolCount);
    }
    
    /**
     * MCP工具统计信息
     */
    public static class McpToolStats {
        private final int totalTools;
        private final int mcpTools;
        private final int thirdPartyMcpTools;
        
        public McpToolStats(int totalTools, int mcpTools, int thirdPartyMcpTools) {
            this.totalTools = totalTools;
            this.mcpTools = mcpTools;
            this.thirdPartyMcpTools = thirdPartyMcpTools;
        }
        
        public int getTotalTools() {
            return totalTools;
        }
        
        public int getMcpTools() {
            return mcpTools;
        }
        
        public int getThirdPartyMcpTools() {
            return thirdPartyMcpTools;
        }
        
        @Override
        public String toString() {
            return String.format("McpToolStats{totalTools=%d, mcpTools=%d, thirdPartyMcpTools=%d}", 
                    totalTools, mcpTools, thirdPartyMcpTools);
        }
    }
}
