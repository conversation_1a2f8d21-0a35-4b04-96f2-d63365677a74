package com.xiaozhi.dialogue.llm.tool.mcp;

import io.modelcontextprotocol.client.McpClient;
import io.modelcontextprotocol.client.transport.HttpClientSseClientTransport;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpHeaders;

import java.net.URI;
import java.net.http.HttpRequest;
import java.util.Map;
import java.util.regex.Pattern;

/**
 * MCP连接工具类，提供统一的MCP客户端创建和连接管理功能
 */
public class McpConnectionUtils {
    private static final Logger logger = LoggerFactory.getLogger(McpConnectionUtils.class);
    
    // 编译后的URL解析正则表达式，避免重复编译
    private static final Pattern URL_PATTERN = Pattern.compile("^(https?://[^/:]+:?\\d*)(/?.*$)");
    
    /**
     * 创建HttpClientSseClientTransport
     *
     * @param endpointUrl MCP端点URL
     * @param headers     可选的请求头
     * @return HttpClientSseClientTransport实例，如果URL无效则返回null
     */
    public static HttpClientSseClientTransport createTransport(String endpointUrl, Map<String, String> headers) {
        HttpRequest.Builder requestBuilder = HttpRequest.newBuilder();

        // 添加自定义请求头
        if (headers != null) {
            headers.forEach(requestBuilder::header);
        }

        var matcher = URL_PATTERN.matcher(endpointUrl);
        if (!matcher.matches()) {
            logger.error("MCP endpoint URL is invalid: {}", endpointUrl);
            return null;
        }

        return HttpClientSseClientTransport
                .builder(matcher.group(1))
                .sseEndpoint(matcher.group(2))
                .requestBuilder(requestBuilder)
                .build();
    }

    /**
     * 创建HttpClientSseClientTransport（使用URI解析方式）
     *
     * @param mcpUrl MCP端点URL
     * @param token  认证令牌
     * @return HttpClientSseClientTransport实例，如果URL无效则返回null
     */
    public static HttpClientSseClientTransport createTransportWithToken(String mcpUrl, String token) {
        try {
            URI uri = new URI(mcpUrl);
            HttpRequest.Builder requestBuilder = HttpRequest.newBuilder();
            
            if (token != null && !token.trim().isEmpty()) {
                requestBuilder.header(HttpHeaders.AUTHORIZATION, "Bearer " + token);
            }
            
            return HttpClientSseClientTransport
                    .builder(uri.toString().replace(uri.getPath(), ""))
                    .sseEndpoint(uri.getPath().isEmpty() ? "/" : uri.getPath())
                    .requestBuilder(requestBuilder)
                    .build();
        } catch (Exception e) {
            logger.error("Failed to create transport for URL: {}", mcpUrl, e);
            return null;
        }
    }



    /**
     * 测试MCP端点连接
     *
     * @param endpointUrl MCP端点URL
     * @param headers     可选的请求头
     * @return 连接是否成功
     */
    public static boolean testConnection(String endpointUrl, Map<String, String> headers) {
        try {
            HttpClientSseClientTransport transport = createTransport(endpointUrl, headers);
            if (transport == null) {
                return false;
            }

            try (var client = McpClient.sync(transport).build()) {
                var initializeResult = client.initialize();
                if (initializeResult == null) {
                    return false;
                }

                client.ping();
                return true;
            }
        } catch (Exception e) {
            logger.debug("Connection test failed for URL: {}", endpointUrl, e);
            return false;
        }
    }

    /**
     * 测试MCP端点连接（使用令牌认证）
     *
     * @param mcpUrl MCP端点URL
     * @param token  认证令牌
     * @return 连接是否成功
     */
    public static boolean testConnectionWithToken(String mcpUrl, String token) {
        try {
            HttpClientSseClientTransport transport = createTransportWithToken(mcpUrl, token);
            if (transport == null) {
                return false;
            }

            try (var client = McpClient.sync(transport).build()) {
                var initializeResult = client.initialize();
                if (initializeResult == null) {
                    return false;
                }

                client.ping();
                return true;
            }
        } catch (Exception e) {
            logger.debug("Connection test failed for URL: {}", mcpUrl, e);
            return false;
        }
    }

    /**
     * 从MCP URL中提取SSE端点路径
     *
     * @param mcpUrl MCP端点URL
     * @return SSE端点路径
     */
    public static String extractSseEndpoint(String mcpUrl) {
        try {
            // 如果URL已经包含SSE端点路径，直接使用
            if (mcpUrl.contains("/sse") || mcpUrl.contains("/mcp_server/sse")) {
                URI uri = new URI(mcpUrl);
                return uri.getPath();
            }

            // 默认的SSE端点路径
            return "/mcp_server/sse";
        } catch (Exception e) {
            logger.warn("Failed to parse MCP URL, using default SSE endpoint: {}", mcpUrl, e);
            return "/mcp_server/sse";
        }
    }
}
