<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xiaozhi.dao.ConfigMapper">

    <sql id="configSql">
        sys_config.configId, sys_config.userId, sys_config.configName, sys_config.configDesc, sys_config.configType, sys_config.modelType, sys_config.provider,
        sys_config.appId, sys_config.apiKey, sys_config.apiSecret, sys_config.ak, sys_config.sk, sys_config.apiUrl, sys_config.isDefault, sys_config.state, sys_config.createTime
    </sql>

    <insert id="add" parameterType="com.xiaozhi.entity.SysConfig">
        INSERT INTO sys_config (userId, configType, modelType, provider, configName, configDesc, appId, apiKey, apiSecret, ak, sk, apiUrl, isDefault)
        VALUES (#{userId}, #{configType}, #{modelType}, #{provider}, #{configName}, #{configDesc}, #{appId}, #{apiKey}, #{apiSecret}, #{ak}, #{sk}, #{apiUrl}, #{isDefault})
    </insert>

    <update id="update" parameterType="com.xiaozhi.entity.SysConfig">
        UPDATE
            sys_config
        <set>
            <if test="configType != null and configType != ''">configType = #{configType},</if>
            <if test="modelType != null and modelType != ''">modelType = #{modelType},</if>
            <if test="provider != null and provider != ''">provider = #{provider},</if>
            <if test="configName != null and configName != ''">configName = #{configName},</if>
            <if test="configDesc != null and configDesc != ''">configDesc = #{configDesc},</if>
            <if test="appId != null and appId != ''">appId = #{appId},</if>
            <if test="apiKey != null and apiKey != ''">apiKey = #{apiKey},</if>
            <if test="apiSecret != null and apiSecret != ''">apiSecret = #{apiSecret},</if>
            <if test="ak != null and ak != ''">ak = #{ak},</if>
            <if test="sk != null and sk != ''">sk = #{sk},</if>
            <if test="apiUrl != null and apiUrl != ''">apiUrl = #{apiUrl},</if>
            <if test="isDefault != null and isDefault != ''">isDefault = #{isDefault},</if>
            <if test="state != null and state != ''">state = #{state},</if>
        </set>
        WHERE
            configId = #{configId}
    </update>

    <update id="resetDefault" parameterType="com.xiaozhi.entity.SysConfig">
        UPDATE
            sys_config
        <set>
            isDefault = '0'
        </set>
        WHERE
            configType = #{configType}
            <if test="modelType != null and modelType != ''">AND modelType = #{modelType}</if>
            AND userId = #{userId}
    </update>

    <select id="query" parameterType="com.xiaozhi.entity.SysConfig" resultType="com.xiaozhi.entity.SysConfig">
        SELECT
        DISTINCT
        <include refid="configSql"></include>
        FROM
            sys_config
        WHERE
            sys_config.state = 1
            <if test="userId != null and userId != ''">AND sys_config.userId = #{userId}</if>
            <if test="isDefault != null and isDefault != ''">AND sys_config.isDefault = #{isDefault}</if>
            <if test="configType != null and configType != ''">AND configType = #{configType}</if>
            <if test="modelType != null and modelType != ''">AND modelType = #{modelType}</if>
            <choose>
                <when test="provider != null and provider != ''">
                    AND provider = #{provider}
                </when>
                <otherwise>
                    AND provider != 'coze'
                    AND provider != 'dify'
                </otherwise>
            </choose>
            <if test="configName != null and configName != ''">AND configName LIKE CONCAT('%', #{configName}, '%')</if>
    </select>

    <select id="selectConfigById" resultType="com.xiaozhi.entity.SysConfig">
        SELECT
        <include refid="configSql"></include>
        FROM
            sys_config
        WHERE
            sys_config.configId = #{configId}
    </select>
</mapper>