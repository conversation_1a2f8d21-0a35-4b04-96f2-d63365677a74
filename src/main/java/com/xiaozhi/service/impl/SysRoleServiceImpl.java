package com.xiaozhi.service.impl;

import com.xiaozhi.common.web.BizError;
import com.xiaozhi.common.web.OhMyLambdaQueryWrapper;
import com.xiaozhi.common.web.Resp;
import com.xiaozhi.dao.RoleMapper;
import com.xiaozhi.dialogue.tts.factory.TtsServiceFactory;
import com.xiaozhi.entity.SysConfig;
import com.xiaozhi.entity.SysRole;
import com.xiaozhi.service.SysConfigService;
import com.xiaozhi.service.SysRoleService;
import com.xiaozhi.vo.RoleCreateParams;
import com.xiaozhi.vo.RoleQueryParams;
import com.xiaozhi.vo.RoleUpdateParams;
import com.xiaozhi.vo.TestVoiceParams;
import io.vavr.control.Either;
import io.vavr.control.Option;
import jakarta.annotation.Resource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 角色操作
 *
 * <AUTHOR>
 */

@Service
public class SysRoleServiceImpl implements SysRoleService {
    private final static String CACHE_NAME = "XiaoZhi:SysRole";

    @Resource
    private RoleMapper roleMapper;

    @Autowired(required = false)
    private CacheManager cacheManager;

    @Resource
    private SysConfigService configService;

    @Resource
    private TtsServiceFactory ttsServiceFactory;

    /**
     * 更新角色信息
     */
    @Transactional(transactionManager = "transactionManager")
    public int update(SysRole role) {
        // 如果当前配置被设置为默认，则将同类型同用户的其他配置设置为非默认
        if (role.getIsDefault()) {
            roleMapper.resetDefault(role);
        }

        int result = roleMapper.update(role);

        // 如果更新成功且roleId不为空，直接将更新后的完整对象加载到缓存中
        if (result > 0 && role.getId() != null && cacheManager != null) {
            // 直接从数据库查询最新数据
            SysRole updatedRole = roleMapper.selectById(role.getId());
            // 手动更新缓存
            if (updatedRole != null) {
                Cache cache = cacheManager.getCache(CACHE_NAME);
                if (cache != null) {
                    cache.put(updatedRole.getId(), updatedRole);
                }
            }
        }

        return result;
    }

    @Override
    @Cacheable(value = CACHE_NAME, key = "#roleId", unless = "#result == null")
    public SysRole selectRoleById(Integer roleId) {
        return roleMapper.selectById(roleId);
    }

    /**
     * 添加角色
     */
    @Override
    public Either<BizError, ?> create(RoleCreateParams params) {
        return Option.of(params)
                .toEither(BizError.BadRequest)
                .map(p -> {
                    SysRole role = new SysRole();
                    role.setUserId(params.getUserId());
                    role.setAvatar(p.getAvatar());
                    role.setName(p.getRoleName());
                    role.setIntro(p.getRoleDesc());
                    role.setVoice(p.getVoiceName());
                    role.setTtsId(p.getTtsId());
                    role.setModelId(p.getModelId());
                    role.setSttId(p.getSttId());
                    // role.setTemperature(p.getTemperature());
                    // role.setTopP(p.getTopP());
                    // role.setVadEnergyTh(p.getVadEnergyTh());
                    // role.setVadSpeechTh(p.getVadSpeechTh());
                    // role.setVadSilenceTh(p.getVadSilenceTh());
                    // role.setVadSilenceMs(p.getVadSilenceMs());
                    role.setIsDefault(p.getIsDefault());

                    // 如果当前配置被设置为默认，则将同类型同用户的其他配置设置为非默认
                    if (role.getIsDefault() != null && role.getIsDefault()) {
                        roleMapper.resetDefault(role);
                    }

                    int result = roleMapper.add(role);
                    if (result > 0) {
                        return Either.<BizError, Object>right(role);
                    } else {
                        return Either.<BizError, Object>left(BizError.SystemError);
                    }
                })
                .fold(Either::left, identity -> identity);
    }

    /**
     * 更新角色
     */
    @Override
    @Transactional(transactionManager = "transactionManager")
    public Either<BizError, ?> update(Integer id, RoleUpdateParams params) {
        return Option.of(roleMapper.selectById(id))
                .toEither(BizError.of(404, "角色不存在"))
                .map(role -> {
                    // 更新角色信息
                    if (params.getAvatar() != null) role.setAvatar(params.getAvatar());
                    if (params.getRoleDesc() != null) role.setName(params.getRoleDesc());
                    if (params.getVoiceName() != null) role.setVoice(params.getVoiceName());
                    if (params.getTtsId() != null) role.setTtsId(params.getTtsId());
                    if (params.getModelId() != null) role.setModelId(params.getModelId());
                    if (params.getSttId() != null) role.setSttId(params.getSttId());
                    if (params.getIsDefault() != null) role.setIsDefault(params.getIsDefault());
                    if (params.getIsEnabled() != null) role.setIsEnabled(params.getIsEnabled());

                    // 如果当前配置被设置为默认，则将同类型同用户的其他配置设置为非默认
                    if (role.getIsDefault()) {
                        roleMapper.resetDefault(role);
                    }

                    int result = roleMapper.update(role);

                    // 更新缓存
                    if (result > 0 && role.getId() != null && cacheManager != null) {
                        SysRole updatedRole = roleMapper.selectById(role.getId());
                        if (updatedRole != null) {
                            Cache cache = cacheManager.getCache(CACHE_NAME);
                            if (cache != null) {
                                cache.put(updatedRole.getId(), updatedRole);
                            }
                        }
                    }

                    if (result > 0) {
                        return Either.<BizError, Object>right(role);
                    } else {
                        return Either.<BizError, Object>left(BizError.SystemError);
                    }
                })
                .fold(Either::left, identity -> identity);
    }

    /**
     * 删除角色
     */
    @Override
    @Transactional(transactionManager = "transactionManager")
    public Either<BizError, ?> delete(Integer id) {
        return Option.of(roleMapper.selectById(id))
                .toEither(BizError.of(404, "角色不存在"))
                .flatMap(role -> {
                    int result = roleMapper.update(role);

                    // 清除缓存
                    if (result > 0 && cacheManager != null) {
                        Cache cache = cacheManager.getCache(CACHE_NAME);
                        if (cache != null) {
                            cache.evict(id);
                        }
                    }

                    if (result > 0) {
                        return Either.right("删除成功");
                    } else {
                        return Either.left(BizError.SystemError);
                    }
                });
    }

    /**
     * 角色列表
     */
    @Override
    public Resp findPage(RoleQueryParams params) {
        var query = new OhMyLambdaQueryWrapper<SysRole>()
                .eq(SysRole::getUserId, params.getUserId())
                .eq(SysRole::getIsEnabled, true)
                .like(SysRole::getName, params.getRoleName());

        var page = roleMapper.selectPage(params.toPage(), query);
        return Resp.from(page);
    }

    /**
     * 测试语音
     */
    @Override
    public Either<BizError, ?> testVoice(TestVoiceParams params) {
        try {
            SysConfig config = null;
            if (!params.getProvider().equals("edge")) {
                config = configService.selectConfigById(params.getTtsId());
            }

            String audioFilePath = ttsServiceFactory.getTtsService(config, params.getVoiceName())
                    .textToSpeech(params.getMessage());

            return Either.right(audioFilePath);
        } catch (IndexOutOfBoundsException e) {
            return Either.left(BizError.of(400, "请先到语音合成配置页面配置对应Key"));
        } catch (Exception e) {
            return Either.left(BizError.SystemError);
        }
    }

}