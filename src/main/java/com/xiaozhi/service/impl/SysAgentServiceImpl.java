package com.xiaozhi.service.impl;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.xiaozhi.common.web.BizError;
import com.xiaozhi.common.web.OhMyLambdaQueryWrapper;
import com.xiaozhi.common.web.Resp;
import com.xiaozhi.dao.ConfigMapper;
import com.xiaozhi.dialogue.token.factory.TokenServiceFactory;
import com.xiaozhi.entity.SysAgent;
import com.xiaozhi.entity.SysConfig;
import com.xiaozhi.service.SysAgentService;
import com.xiaozhi.service.SysConfigService;
import com.xiaozhi.vo.AgentCreateParams;
import com.xiaozhi.vo.AgentQueryParams;
import io.vavr.control.Either;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.io.IOException;
import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.time.Duration;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 智能体服务实现
 *
 * <AUTHOR>
 */
@Service
public class SysAgentServiceImpl implements SysAgentService {

    private static final Logger logger = LoggerFactory.getLogger(SysAgentServiceImpl.class);

    @Resource
    private ConfigMapper configMapper;

    @Resource
    private TokenServiceFactory tokenService;

    private final HttpClient httpClient = HttpClient.newBuilder()
            .connectTimeout(Duration.ofSeconds(10))
            .build();
    private final ObjectMapper objectMapper = new ObjectMapper();

    @Resource
    private SysConfigService configService;

    /**
     * 删除智能体
     *
     * @param agentId 智能体ID
     * @return 结果
     */
    @Override
    @Transactional(transactionManager = "transactionManager")
    public Either<BizError, ?> delete(Integer agentId) {
        return null;
    }

    /**
     * 查询智能体列表 - 同步版本
     *
     * @return 智能体集合
     */
    @Override
    public Resp findPage(AgentQueryParams params) {
        var agents = params.getProvider()
                .map(provider -> switch (provider) {
                    case "coze" -> getCozeAgents(params);
                    case "dify" -> getDifyAgents(params);
                    default -> List.of();
                })
                .orElse(List.of());

        return Resp.of(agents, agents.size());
    }

    @Override
    public Either<BizError, ?> create(AgentCreateParams params) {
        return null;
    }

    @Override
    public Either<BizError, ?> update(Integer id, SysAgent params) {
        return null;
    }

    /**
     * 从DIFY API获取智能体信息，并与数据库同步
     *
     * @return 智能体集合
     */
    private List<SysAgent> getDifyAgents(AgentQueryParams params) {
        List<SysAgent> agentList = new ArrayList<>();

        // 查询所有类型的Dify配置
        var configs = configMapper.selectList(
                new OhMyLambdaQueryWrapper<SysConfig>()
                        .eq(SysConfig::getProvider, params.getProvider())
                        .eq(SysConfig::getUserId, params.getUserId())
        );

        if (ObjectUtils.isEmpty(configs)) {
            return agentList;
        }

        List<SysConfig> agentConfigs = configs.stream()
                .filter(config -> "agent".equals(config.getType()))
                .collect(Collectors.toList());

        // 创建一个Map来存储llm配置，以apiKey为键
        Map<String, SysConfig> llmConfigMap = new HashMap<>();
        configs.stream()
                .filter(config -> "llm".equals(config.getType()))
                .forEach(config -> {
                    if (config.getApiKey() != null) {
                        llmConfigMap.put(config.getApiKey(), config);
                    }
                });

        // 处理每个agent配置
        for (SysConfig agentConfig : agentConfigs) {
            String apiKey = agentConfig.getApiKey();
            String apiUrl = agentConfig.getApiUrl();
            Integer configId = agentConfig.getId();
            Integer userId = agentConfig.getUserId();


            // 检查是否已存在对应的llm配置
            SysConfig existingLlmConfig = llmConfigMap.get(apiKey);

            // 如果已存在llm配置，直接创建Agent对象返回
            if (existingLlmConfig != null) {
                SysAgent agent = new SysAgent();
                agent.setConfigId(existingLlmConfig.getId());
                agent.setProvider("dify");
                agent.setApiKey(apiKey);
                agent.setName(existingLlmConfig.getName());
                agent.setIntro(existingLlmConfig.getIntro());
                agent.setIsDefault(existingLlmConfig.getIsDefault());
                agent.setPublishTime(existingLlmConfig.getCreatedAt());

                // 如果前端传入了智能体名称过滤条件，则进行过滤
                params.getName().ifPresentOrElse(
                        name -> {
                            if (agent.getName().toLowerCase().contains(name.toLowerCase())) {
                                agentList.add(agent);
                            }
                        },
                        () -> agentList.add(agent)
                );
            } else {
                // 如果不存在llm配置，调用API获取信息并创建新的llm配置
                SysAgent agent = new SysAgent();
                agent.setConfigId(configId);
                agent.setProvider("dify");
                agent.setApiKey(apiKey);

                try {
                    // 调用info API
                    HttpRequest infoRequest = HttpRequest.newBuilder()
                            .uri(URI.create(apiUrl + "/info"))
                            .header("Authorization", "Bearer " + apiKey)
                            .header("Content-Type", "application/json")
                            .GET()
                            .build();

                    HttpResponse<String> infoResponse = httpClient.send(infoRequest,
                            HttpResponse.BodyHandlers.ofString());

                    if (infoResponse.statusCode() == 200) {
                        JsonNode infoNode = objectMapper.readTree(infoResponse.body());
                        String name = infoNode.has("name") ? infoNode.get("name").asText() : "DIFY Agent";
                        String description = infoNode.has("description") ? infoNode.get("description").asText() : "";

                        agent.setName(name);
                        agent.setIntro(description);

                        // 创建新的llm配置
                        SysConfig newLlmConfig = new SysConfig();
                        newLlmConfig.setUserId(userId);
                        newLlmConfig.setType("llm");
                        newLlmConfig.setProvider("dify");
                        newLlmConfig.setApiKey(apiKey);
                        newLlmConfig.setName(name);
                        newLlmConfig.setIntro(description);
                        newLlmConfig.setApiUrl(apiUrl);
                        newLlmConfig.setIsEnabled(true);  // 默认启用

                        // 添加到数据库
                        try {
                            configMapper.add(newLlmConfig);
                            logger.debug("添加DIFY LLM配置成功: {}", apiKey);
                            agent.setConfigId(newLlmConfig.getId());
                        } catch (Exception e) {
                            logger.error("添加DIFY LLM配置失败: {}", e.getMessage());
                        }

                        // 获取图标信息
                        try {
                            HttpRequest metaRequest = HttpRequest.newBuilder()
                                    .uri(URI.create(apiUrl + "/meta"))
                                    .header("Authorization", "Bearer " + apiKey)
                                    .header("Content-Type", "application/json")
                                    .GET()
                                    .build();

                            HttpResponse<String> metaResponse = httpClient.send(metaRequest,
                                    HttpResponse.BodyHandlers.ofString());

                            if (metaResponse.statusCode() == 200) {
                                JsonNode metaNode = objectMapper.readTree(metaResponse.body());
                                if (metaNode.has("tool_icons") && metaNode.get("tool_icons").has("api_tool")) {
                                    JsonNode apiTool = metaNode.get("tool_icons").get("api_tool");
                                    if (apiTool.has("content")) {
                                        String iconContent = apiTool.get("content").asText();
                                        agent.setIconUrl(iconContent);
                                    }
                                }
                            }
                        } catch (Exception e) {
                            logger.error("获取DIFY meta信息异常", e);
                        }
                    }
                } catch (Exception e) {
                    logger.error("查询DIFY智能体信息异常", e);
                    agent.setName(agentConfig.getName() != null ? agentConfig.getName() : "DIFY Agent");
                    agent.setIntro("无法连接到DIFY API");
                }

                // 如果前端传入了智能体名称过滤条件，则进行过滤
                params.getName().ifPresentOrElse(
                        name -> {
                            if (agent.getName().toLowerCase().contains(name.toLowerCase())) {
                                agentList.add(agent);
                            }
                        },
                        () -> agentList.add(agent)
                );
            }
        }

        return agentList;
    }

    /**
     * 从Coze API获取智能体列表，并与数据库同步
     *
     * @return 智能体集合
     */
    private List<SysAgent> getCozeAgents(AgentQueryParams params) {
        List<SysAgent> agentList = new ArrayList<>();

        var configs = configMapper.selectList(
                new OhMyLambdaQueryWrapper<SysConfig>()
                        .eq(SysConfig::getProvider, params.getProvider())
                        .eq(SysConfig::getUserId, params.getUserId())
        );
        if (ObjectUtils.isEmpty(configs)) {
            return agentList;
        }

        var config = configs.get(0);
        String spaceId = config.getApiSecret();

        // 普通用户应该只能查询使用管理员配置的内容
        Integer userId = config.getUserId();

        String token = tokenService.getTokenService(config).getToken();

        try {
            // 调用Coze API获取智能体列表
            HttpRequest request = HttpRequest.newBuilder()
                    .uri(URI.create("https://api.coze.cn/v1/space/published_bots_list?space_id=" + spaceId))
                    .header("Authorization", "Bearer " + token)
                    .header("Content-Type", "application/json")
                    .GET()
                    .build();

            HttpResponse<String> response = httpClient.send(request, HttpResponse.BodyHandlers.ofString());

            if (response.statusCode() == 200) {
                JsonNode rootNode = objectMapper.readTree(response.body());
                if (rootNode.has("code") && rootNode.get("code").asInt() == 0) {
                    JsonNode spaceBots = rootNode.path("data").path("space_bots");

                    // 查询数据库中现有的所有与当前用户相关的coze智能体配置
                    SysConfig existingQueryConfig = new SysConfig();
                    existingQueryConfig.setUserId(userId);
                    existingQueryConfig.setType("llm");
                    existingQueryConfig.setProvider("coze");
                    List<SysConfig> existingConfigs = configMapper.query(existingQueryConfig);

                    // 创建一个Map来存储现有的配置，以botId为键
                    Map<String, SysConfig> existingConfigMap = new HashMap<>();
                    for (SysConfig existingConfig : existingConfigs) {
                        if (existingConfig.getName() != null) {
                            existingConfigMap.put(existingConfig.getName(), existingConfig);
                        }
                    }

                    // 记录API返回的所有botId，用于后续比对删除
                    List<String> apiBotIds = new ArrayList<>();

                    // 遍历智能体列表
                    for (JsonNode botNode : spaceBots) {
                        String botId = botNode.path("bot_id").asText();
                        String botName = botNode.path("bot_name").asText();
                        String description = botNode.path("description").asText();
                        String iconUrl = botNode.path("icon_url").asText();
                        long publishTime = Long.parseLong(botNode.path("publish_time").asText());

                        apiBotIds.add(botId);

                        // 创建SysAgent对象用于返回
                        SysAgent botAgent = new SysAgent();
                        botAgent.setBotId(botId);
                        botAgent.setName(botName);
                        botAgent.setIntro(description);
                        botAgent.setIconUrl(iconUrl);
                        botAgent.setPublishTime(new Date(publishTime * 1000));
                        botAgent.setProvider("coze");

                        // 同步到数据库
                        // 检查是否已存在该botId的配置
                        if (existingConfigMap.containsKey(botId)) {
                            // 存在则更新
                            SysConfig existingConfig = existingConfigMap.get(botId);
                            existingConfig.setName(botId);
                            existingConfig.setIntro(description);
                            // 如果数据库已存在，返回对应 ConfigId 为前端设备绑定使用
                            botAgent.setConfigId(existingConfig.getId());
                            botAgent.setIsDefault(existingConfig.getIsDefault());

                            // 更新配置
                            try {
                                configMapper.update(existingConfig);
                            } catch (Exception e) {
                                logger.error("更新智能体配置失败: {}", e.getMessage());
                            }
                        } else {
                            // 不存在则新增
                            SysConfig newConfig = new SysConfig();
                            newConfig.setUserId(userId);
                            newConfig.setType("llm");
                            newConfig.setProvider("coze");
                            newConfig.setName(botId);
                            newConfig.setIntro(description);
                            newConfig.setIsEnabled(true);  // 默认启用
                            configMapper.add(newConfig);
                        }

                        // 如果前端传入了智能体名称过滤条件，则进行过滤
                        params.getName().ifPresentOrElse(
                                name -> {
                                    if (botAgent.getName().toLowerCase().contains(name.toLowerCase())) {
                                        agentList.add(botAgent);
                                    }
                                },
                                () -> agentList.add(botAgent)
                        );
                    }

                    // 删除不再存在的智能体配置 (暂时注释掉，与原代码保持一致)
                    // for (String existingBotId : existingConfigMap.keySet()) {
                    //     if (!apiBotIds.contains(existingBotId)) {
                    //         SysConfig configToDelete = existingConfigMap.get(existingBotId);
                    //         try {
                    //             configMapper.delete(configToDelete.getConfigId());
                    //             logger.debug("删除智能体配置成功: {}", existingBotId);
                    //         } catch (Exception e) {
                    //             logger.error("删除智能体配置失败: {}", e.getMessage());
                    //         }
                    //     }
                    // }
                } else {
                    String errorMsg = rootNode.has("msg") ? rootNode.get("msg").asText() : "未知错误";
                    logger.error("查询Coze智能体列表失败：{}", errorMsg);
                }
            }
        } catch (IOException | InterruptedException e) {
            logger.error("查询Coze智能体列表异常", e);
        }

        return agentList;
    }
}