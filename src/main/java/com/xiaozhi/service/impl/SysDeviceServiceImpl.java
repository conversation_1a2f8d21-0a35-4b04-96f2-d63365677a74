package com.xiaozhi.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.xiaozhi.common.web.BizError;
import com.xiaozhi.common.web.Resp;
import com.xiaozhi.communication.common.SessionManager;
import com.xiaozhi.dao.*;
import com.xiaozhi.entity.SysDevice;
import com.xiaozhi.entity.SysRole;
import com.xiaozhi.service.SysDeviceService;
import com.xiaozhi.utils.CommonUtils;
import com.xiaozhi.vo.DeviceCreateParams;
import com.xiaozhi.vo.DeviceQueryParams;
import io.vavr.control.Either;
import io.vavr.control.Option;
import jakarta.annotation.Resource;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;


@Service
public class SysDeviceServiceImpl implements SysDeviceService {

    private final static String CACHE_NAME = "XiaoZhi:SysDevice";

    @Resource
    private DeviceMapper deviceMapper;

    @Resource
    private RoleMapper roleMapper;

    @Resource
    private SessionManager sessionManager;

    @Resource
    private CodeMapper codeMapper;

    /**
     * 添加设备
     */
    @Override
    public Either<BizError, ?> add(DeviceCreateParams params) {
        return Option.of(codeMapper.findByCode(params.getCode()))
                .toEither(BizError.InvalidActivationCode)
                .flatMap(code -> {
                    var roleQuery = new LambdaQueryWrapper<SysRole>()
                            .eq(SysRole::getUserId, params.getUserId())
                            .eq(SysRole::getIsDefault, true)
                            .select(SysRole::getId);

                    return Option.of(roleMapper.selectOne(roleQuery))
                            .toEither(BizError.NoDefaultRole)
                            .map(role -> {
                                var device = new SysDevice()
                                        .setDeviceId(code.getDeviceId())
                                        .setDeviceName(code.getType())
                                        .setType(code.getType());
                                device.setRoleId(role.getId());
                                device.setUserId(params.getUserId());
                                deviceMapper.insert(device);
                                return true;
                            });
                });
    }

    @Override
    public Either<BizError, ?> update(Integer id, SysDevice device) {
        return Option.of(deviceMapper.selectById(id))
                .toEither(BizError.UserNotExists)
                .map(_ -> {
                    device.setId(id);
                    deviceMapper.updateById(device);
                    return true;
                });
    }

    @Override
    public Either<BizError, ?> delete(Integer id) {
        return Option.of(deviceMapper.selectById(id))
                .toEither(BizError.UserNotExists)
                .map(deviceMapper::deleteById)
                .map(__ -> true);
    }

    /**
     * 查询设备列表
     */
    @Override
    public Resp findPage(DeviceQueryParams params, Integer userId) {
        var query = new LambdaQueryWrapper<SysDevice>();

        // 如果userId不为null，则按用户过滤
        if (userId != null) {
            query.eq(SysDevice::getUserId, userId);
        }

        // 添加查询条件
        params.getDeviceId().ifPresent(deviceId ->
                query.like(SysDevice::getDeviceId, deviceId));

        var page = deviceMapper.selectPage(params.toPage(), query);
        return Resp.from(page);
    }

    @Override
    @Cacheable(value = CACHE_NAME, key = "#deviceId.replace(\":\", \"-\")", unless = "#result == null")
    public SysDevice findByDeviceId(String deviceId) {
        return deviceMapper.selectDeviceById(deviceId);
    }

    /**
     * 查询并生成验证码
     */
    @Override
    public SysDevice generateCode(SysDevice device) {
        var code = CommonUtils.generateActivationCode();
        SysDevice result = deviceMapper.queryVerifyCode(device);
        if (result == null) {
            result = new SysDevice();
            deviceMapper.generateCode(device);
            // result.setCode(device.getCode());
        }
        return result;
    }

    /**
     * 关系设备验证码语音路径
     */
    @Override
    public int updateCode(SysDevice device) {
        return deviceMapper.updateCode(device);
    }

    /**
     * 直接插入设备记录（用于OTA时保存未绑定设备）
     */
    @Override
    public int insertDevice(SysDevice device) {
        return deviceMapper.insert(device);
    }

}