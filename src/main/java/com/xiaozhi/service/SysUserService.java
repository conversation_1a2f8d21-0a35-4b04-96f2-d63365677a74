package com.xiaozhi.service;

import com.xiaozhi.common.web.BizError;
import com.xiaozhi.common.web.PageFilter;
import com.xiaozhi.common.web.Resp;
import com.xiaozhi.entity.SysUser;
import com.xiaozhi.vo.EmailCaptchaParams;
import com.xiaozhi.vo.UserCreateParams;
import com.xiaozhi.vo.UserQueryParams;
import com.xiaozhi.vo.UserUpdateParams;
import io.vavr.control.Either;

import java.util.List;

/**
 * 用户操作
 * 
 * <AUTHOR>
 * 
 */
public interface SysUserService {

    /**
     * 用户名sessionkey
     */
    public static final String USER_SESSIONKEY = "user_sessionkey";

    /**
     * 查询用户信息
     * 
     * @param username
     * @return 用户信息
     */
    SysUser query(String username);

    /**
     * 用户查询列表
     * 
     * @param user
     * @return 用户列表
     */
    List<SysUser> queryUsers(SysUser user, PageFilter pageFilter);

    SysUser selectUserByUserId(Integer userId);

    SysUser selectUserByUsername(String username);

    SysUser selectUserByEmail(String email);

    /**
     * 新增用户
     * 
     * @param user
     * @return
     */
    int add(SysUser user);

    /**
     * 修改用户信息
     * 
     * @param user
     * @return
     */
    int update(SysUser user);

    /**
     * 查询验证码是否有效
     *
     * @param code
     * @param email
     * @return
     */
    int queryCaptcha(String code, String email);

    // ========== 新的 DeviceController 风格方法 ==========

    /**
     * 添加用户 - 新风格
     */
    Either<BizError, ?> add(UserCreateParams params);

    /**
     * 更新用户 - 新风格
     */
    Either<BizError, ?> update(Integer id, UserUpdateParams params);

    /**
     * 删除用户 - 新风格
     */
    Either<BizError, ?> delete(Integer id);

    /**
     * 用户列表 - 新风格
     */
    Resp findPage(UserQueryParams params);

    /**
     * 发送邮箱验证码 - 新风格
     */
    Either<BizError, ?> sendEmailCaptcha(EmailCaptchaParams params);

    /**
     * 验证验证码 - 新风格
     */
    Either<BizError, ?> checkCaptcha(String code, String email);

    /**
     * 检查用户名和邮箱是否已存在 - 新风格
     */
    Either<BizError, ?> checkUser(String username, String email);
}