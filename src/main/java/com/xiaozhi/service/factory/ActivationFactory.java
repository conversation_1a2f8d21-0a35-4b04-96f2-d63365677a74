package com.xiaozhi.service.factory;

import com.xiaozhi.config.ActivationCodeConfig;
import com.xiaozhi.service.ActivationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;

/**
 * 激活码服务工厂
 * 根据配置选择使用 MySQL 或 Redis 实现
 */
@Slf4j
@Component
public class ActivationFactory {

    @Resource
    private ApplicationContext applicationContext;

    @Resource
    private ActivationCodeConfig activationCodeConfig;

    private ActivationService activationService;

    @PostConstruct
    public void init() {
        String beanName = switch (activationCodeConfig.getStorageType()) {
            case MYSQL -> "mysqlActivationCodeService";
            case REDIS -> "redisActivationCodeService";
        };

        try {
            activationService = applicationContext.getBean(beanName, ActivationService.class);
            log.info("激活码服务初始化完成，使用实现: {} ({})", 
                    beanName, activationCodeConfig.getStorageType());
        } catch (Exception e) {
            log.error("激活码服务初始化失败，bean名称: {}", beanName, e);
            throw new RuntimeException("激活码服务初始化失败", e);
        }
    }

    /**
     * 获取激活码服务实例
     * 
     * @return 激活码服务实例
     */
    public ActivationService getService() {
        if (activationService == null) {
            throw new IllegalStateException("激活码服务未初始化");
        }
        return activationService;
    }

    /**
     * 获取当前使用的存储类型
     * 
     * @return 存储类型
     */
    public ActivationCodeConfig.StorageType getStorageType() {
        return activationCodeConfig.getStorageType();
    }
}
