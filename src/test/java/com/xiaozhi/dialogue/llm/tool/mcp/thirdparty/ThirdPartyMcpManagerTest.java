package com.xiaozhi.dialogue.llm.tool.mcp.thirdparty;

import com.xiaozhi.config.McpConfig;
import com.xiaozhi.entity.SysMcpEndpoint;
import com.xiaozhi.service.SysMcpEndpointService;
import org.junit.jupiter.api.Test;
import org.springframework.ai.tool.function.FunctionToolCallback;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.TestPropertySource;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@SpringBootTest(classes = {ThirdPartyMcpManager.class, ThirdPartyMcpService.class, McpConfig.class})
@TestPropertySource(properties = {
    "xiaozhi.mcp.connection.timeout-ms=5000",
    "xiaozhi.mcp.connection.retry-count=1",
    "xiaozhi.mcp.connection.retry-interval-ms=100"
})
class ThirdPartyMcpManagerTest {

    @Autowired
    private ThirdPartyMcpManager thirdPartyMcpManager;

    @MockBean
    private SysMcpEndpointService mcpEndpointService;

    @MockBean
    private ThirdPartyMcpService thirdPartyMcpService;

    @Test
    void testManagerInitialization() {
        assertNotNull(thirdPartyMcpManager);
    }

    @Test
    void testConnectAndDisconnect() {
        // Test with a dummy endpoint - this will fail but we can test the flow
        boolean connected = thirdPartyMcpManager.connectToEndpoint(
            "http://localhost:12345/invalid-endpoint",
            Map.of("Authorization", "Bearer test-token")
        );

        // Should fail to connect to invalid endpoint
        assertFalse(connected);

        // Should not be connected
        assertFalse(thirdPartyMcpManager.isConnectedToEndpoint("http://localhost:12345/invalid-endpoint"));

        // Should return empty list for tools
        assertTrue(thirdPartyMcpManager.getToolsFromEndpoint("http://localhost:12345/invalid-endpoint").isEmpty());

        // Disconnect should handle gracefully even for non-connected endpoint
        // We're not asserting the return value as it may vary depending on implementation details
        thirdPartyMcpManager.disconnectFromEndpoint("http://localhost:12345/invalid-endpoint");
    }

    @Test
    void testInitializeEnabledEndpoints_Success() {
        // Arrange
        SysMcpEndpoint endpoint1 = new SysMcpEndpoint();
        endpoint1.setUrl("http://example.com/mcp1");
        endpoint1.setToken("token1");
        endpoint1.setHeaders("{\"Custom-Header\": \"value1\"}");

        SysMcpEndpoint endpoint2 = new SysMcpEndpoint();
        endpoint2.setUrl("http://example.com/mcp2");
        endpoint2.setToken("token2");
        endpoint2.setHeaders(null);

        List<SysMcpEndpoint> enabledEndpoints = Arrays.asList(endpoint1, endpoint2);

        FunctionToolCallback mockTool1 = mock(FunctionToolCallback.class);
        FunctionToolCallback mockTool2 = mock(FunctionToolCallback.class);

        when(mcpEndpointService.getEnabledEndpoints()).thenReturn(enabledEndpoints);
        when(thirdPartyMcpService.connectAndRegisterTools(eq("http://example.com/mcp1"), any()))
            .thenReturn(Arrays.asList(mockTool1));
        when(thirdPartyMcpService.connectAndRegisterTools(eq("http://example.com/mcp2"), any()))
            .thenReturn(Arrays.asList(mockTool2));

        // Act
        int result = thirdPartyMcpManager.initializeEnabledEndpoints();

        // Assert
        assertEquals(2, result);
        verify(mcpEndpointService, times(1)).getEnabledEndpoints();
        verify(thirdPartyMcpService, times(2)).connectAndRegisterTools(anyString(), any());
    }

    @Test
    void testInitializeEnabledEndpoints_NoEndpoints() {
        // Arrange
        when(mcpEndpointService.getEnabledEndpoints()).thenReturn(Collections.emptyList());

        // Act
        int result = thirdPartyMcpManager.initializeEnabledEndpoints();

        // Assert
        assertEquals(0, result);
        verify(mcpEndpointService, times(1)).getEnabledEndpoints();
        verify(thirdPartyMcpService, never()).connectAndRegisterTools(anyString(), any());
    }

    @Test
    void testInitializeEnabledEndpoints_PartialFailure() {
        // Arrange
        SysMcpEndpoint endpoint1 = new SysMcpEndpoint();
        endpoint1.setUrl("http://example.com/mcp1");

        SysMcpEndpoint endpoint2 = new SysMcpEndpoint();
        endpoint2.setUrl("http://example.com/mcp2");

        List<SysMcpEndpoint> enabledEndpoints = Arrays.asList(endpoint1, endpoint2);

        FunctionToolCallback mockTool = mock(FunctionToolCallback.class);

        when(mcpEndpointService.getEnabledEndpoints()).thenReturn(enabledEndpoints);
        when(thirdPartyMcpService.connectAndRegisterTools(eq("http://example.com/mcp1"), any()))
            .thenReturn(Arrays.asList(mockTool));
        when(thirdPartyMcpService.connectAndRegisterTools(eq("http://example.com/mcp2"), any()))
            .thenReturn(Collections.emptyList()); // Simulate connection failure

        // Act
        int result = thirdPartyMcpManager.initializeEnabledEndpoints();

        // Assert
        assertEquals(1, result); // Only one successful connection
        verify(mcpEndpointService, times(1)).getEnabledEndpoints();
        verify(thirdPartyMcpService, times(2)).connectAndRegisterTools(anyString(), any());
    }

    @Test
    void testGetAllTools() {
        // This test would require setting up the internal state
        // For now, we'll just test that the method doesn't throw
        assertDoesNotThrow(() -> {
            List<FunctionToolCallback> tools = thirdPartyMcpManager.getAllTools();
            assertNotNull(tools);
        });
    }

    @Test
    void testCleanup() {
        // Act & Assert - should not throw
        assertDoesNotThrow(() -> thirdPartyMcpManager.cleanup());
    }

    @Test
    void testReloadEndpoints() {
        // Arrange
        when(mcpEndpointService.getEnabledEndpoints()).thenReturn(Collections.emptyList());

        // Act & Assert - should not throw
        assertDoesNotThrow(() -> {
            int result = thirdPartyMcpManager.reloadEndpoints();
            assertEquals(0, result);
        });
    }
}