package com.xiaozhi.dialogue.llm.tool.mcp;

import com.xiaozhi.communication.common.ChatSession;
import com.xiaozhi.dialogue.llm.tool.ToolsSessionHolder;
import com.xiaozhi.dialogue.llm.tool.mcp.device.DeviceMcpService;
import com.xiaozhi.dialogue.llm.tool.mcp.thirdparty.ThirdPartyMcpManager;
import com.xiaozhi.entity.SysDevice;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.ai.tool.function.FunctionToolCallback;
import org.springframework.ai.tool.definition.ToolDefinition;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class McpToolRegistrationServiceTest {

    @Mock
    private DeviceMcpService deviceMcpService;

    @Mock
    private ThirdPartyMcpManager thirdPartyMcpManager;

    @Mock
    private ChatSession chatSession;

    @Mock
    private ToolsSessionHolder toolsSessionHolder;

    @InjectMocks
    private McpToolRegistrationService mcpToolRegistrationService;

    @BeforeEach
    void setUp() {
        when(chatSession.getSessionId()).thenReturn("test-session-123");
        when(chatSession.getToolsSessionHolder()).thenReturn(toolsSessionHolder);
        when(chatSession.getSysDevice()).thenReturn(new SysDevice());
    }

    @Test
    void testInitializeAllMcpTools_Success() {
        // Arrange
        FunctionToolCallback mockTool1 = mock(FunctionToolCallback.class);
        FunctionToolCallback mockTool2 = mock(FunctionToolCallback.class);
        ToolDefinition toolDef1 = mock(ToolDefinition.class);
        ToolDefinition toolDef2 = mock(ToolDefinition.class);

        when(mockTool1.getToolDefinition()).thenReturn(toolDef1);
        when(mockTool2.getToolDefinition()).thenReturn(toolDef2);
        when(toolDef1.name()).thenReturn("mcp_test_tool_1");
        when(toolDef2.name()).thenReturn("mcp_test_tool_2");

        List<FunctionToolCallback> thirdPartyTools = Arrays.asList(mockTool1, mockTool2);
        
        when(thirdPartyMcpManager.initializeEnabledEndpoints()).thenReturn(2);
        when(thirdPartyMcpManager.getAllTools()).thenReturn(thirdPartyTools);

        // Act
        mcpToolRegistrationService.initializeAllMcpTools(chatSession);

        // Assert
        verify(deviceMcpService, times(1)).initialize(chatSession);
        verify(thirdPartyMcpManager, times(1)).initializeEnabledEndpoints();
        verify(thirdPartyMcpManager, times(1)).getAllTools();
        verify(toolsSessionHolder, times(2)).registerFunction(anyString(), any(FunctionToolCallback.class));
        verify(toolsSessionHolder).registerFunction("mcp_test_tool_1", mockTool1);
        verify(toolsSessionHolder).registerFunction("mcp_test_tool_2", mockTool2);
    }

    @Test
    void testInitializeAllMcpTools_NoThirdPartyTools() {
        // Arrange
        when(thirdPartyMcpManager.initializeEnabledEndpoints()).thenReturn(0);
        when(thirdPartyMcpManager.getAllTools()).thenReturn(Collections.emptyList());

        // Act
        mcpToolRegistrationService.initializeAllMcpTools(chatSession);

        // Assert
        verify(deviceMcpService, times(1)).initialize(chatSession);
        verify(thirdPartyMcpManager, times(1)).initializeEnabledEndpoints();
        verify(thirdPartyMcpManager, times(1)).getAllTools();
        verify(toolsSessionHolder, never()).registerFunction(anyString(), any(FunctionToolCallback.class));
    }

    @Test
    void testInitializeAllMcpTools_DeviceMcpServiceThrowsException() {
        // Arrange
        doThrow(new RuntimeException("Device MCP initialization failed")).when(deviceMcpService).initialize(chatSession);
        
        FunctionToolCallback mockTool = mock(FunctionToolCallback.class);
        ToolDefinition toolDef = mock(ToolDefinition.class);
        when(mockTool.getToolDefinition()).thenReturn(toolDef);
        when(toolDef.name()).thenReturn("mcp_test_tool");
        when(thirdPartyMcpManager.initializeEnabledEndpoints()).thenReturn(1);
        when(thirdPartyMcpManager.getAllTools()).thenReturn(Arrays.asList(mockTool));

        // Act & Assert - should not throw exception
        assertDoesNotThrow(() -> mcpToolRegistrationService.initializeAllMcpTools(chatSession));

        // Third party tools should still be initialized
        verify(thirdPartyMcpManager, times(1)).initializeEnabledEndpoints();
        verify(toolsSessionHolder, times(1)).registerFunction("mcp_test_tool", mockTool);
    }

    @Test
    void testInitializeAllMcpTools_ThirdPartyMcpManagerThrowsException() {
        // Arrange
        when(thirdPartyMcpManager.initializeEnabledEndpoints()).thenThrow(new RuntimeException("Third party MCP initialization failed"));

        // Act & Assert - should not throw exception
        assertDoesNotThrow(() -> mcpToolRegistrationService.initializeAllMcpTools(chatSession));

        // Device MCP should still be initialized
        verify(deviceMcpService, times(1)).initialize(chatSession);
    }

    @Test
    void testInitializeDeviceMcpTools() {
        // Act
        mcpToolRegistrationService.initializeDeviceMcpTools(chatSession);

        // Assert
        verify(deviceMcpService, times(1)).initialize(chatSession);
        verify(thirdPartyMcpManager, never()).initializeEnabledEndpoints();
        verify(thirdPartyMcpManager, never()).getAllTools();
    }

    @Test
    void testInitializeThirdPartyMcpToolsOnly() {
        // Arrange
        FunctionToolCallback mockTool = mock(FunctionToolCallback.class);
        ToolDefinition toolDef = mock(ToolDefinition.class);
        when(mockTool.getToolDefinition()).thenReturn(toolDef);
        when(toolDef.name()).thenReturn("mcp_third_party_tool");
        when(thirdPartyMcpManager.initializeEnabledEndpoints()).thenReturn(1);
        when(thirdPartyMcpManager.getAllTools()).thenReturn(Arrays.asList(mockTool));

        // Act
        mcpToolRegistrationService.initializeThirdPartyMcpToolsOnly(chatSession);

        // Assert
        verify(deviceMcpService, never()).initialize(chatSession);
        verify(thirdPartyMcpManager, times(1)).initializeEnabledEndpoints();
        verify(toolsSessionHolder, times(1)).registerFunction("mcp_third_party_tool", mockTool);
    }

    @Test
    void testReloadThirdPartyMcpTools() {
        // Arrange
        FunctionToolCallback mockTool = mock(FunctionToolCallback.class);
        ToolDefinition toolDef = mock(ToolDefinition.class);
        when(mockTool.getToolDefinition()).thenReturn(toolDef);
        when(toolDef.name()).thenReturn("mcp_reloaded_tool");
        when(thirdPartyMcpManager.reloadEndpoints()).thenReturn(1);
        when(thirdPartyMcpManager.getAllTools()).thenReturn(Arrays.asList(mockTool));

        // Act
        mcpToolRegistrationService.reloadThirdPartyMcpTools(chatSession);

        // Assert
        verify(thirdPartyMcpManager, times(1)).reloadEndpoints();
        verify(thirdPartyMcpManager, times(1)).getAllTools();
        verify(toolsSessionHolder, times(1)).registerFunction("mcp_reloaded_tool", mockTool);
    }

    @Test
    void testGetMcpToolStats() {
        // Arrange
        FunctionToolCallback mcpTool1 = mock(FunctionToolCallback.class);
        FunctionToolCallback mcpTool2 = mock(FunctionToolCallback.class);
        FunctionToolCallback regularTool = mock(FunctionToolCallback.class);

        ToolDefinition mcpToolDef1 = mock(ToolDefinition.class);
        ToolDefinition mcpToolDef2 = mock(ToolDefinition.class);
        ToolDefinition regularToolDef = mock(ToolDefinition.class);

        when(mcpTool1.getToolDefinition()).thenReturn(mcpToolDef1);
        when(mcpTool2.getToolDefinition()).thenReturn(mcpToolDef2);
        when(regularTool.getToolDefinition()).thenReturn(regularToolDef);

        when(mcpToolDef1.name()).thenReturn("mcp_tool_1");
        when(mcpToolDef2.name()).thenReturn("mcp_tool_2");
        when(regularToolDef.name()).thenReturn("regular_tool");
        
        List<org.springframework.ai.tool.ToolCallback> allTools = Arrays.asList(mcpTool1, mcpTool2, regularTool);
        when(chatSession.getToolCallbacks()).thenReturn(allTools);
        
        when(thirdPartyMcpManager.getAllTools()).thenReturn(Arrays.asList(mcpTool1, mcpTool2));

        // Act
        McpToolRegistrationService.McpToolStats stats = mcpToolRegistrationService.getMcpToolStats(chatSession);

        // Assert
        assertEquals(3, stats.getTotalTools());
        assertEquals(2, stats.getMcpTools());
        assertEquals(2, stats.getThirdPartyMcpTools());
    }

    @Test
    void testMcpToolStats_ToString() {
        // Arrange
        McpToolRegistrationService.McpToolStats stats = new McpToolRegistrationService.McpToolStats(10, 5, 3);

        // Act
        String result = stats.toString();

        // Assert
        assertEquals("McpToolStats{totalTools=10, mcpTools=5, thirdPartyMcpTools=3}", result);
    }
}
