package com.xiaozhi.dialogue.llm.tool.mcp.thirdparty;

import com.xiaozhi.config.McpConfig;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest(classes = {ThirdPartyMcpService.class, McpConfig.class})
class ThirdPartyMcpServiceTest {
    
    @Autowired
    private ThirdPartyMcpService thirdPartyMcpService;
    
    @Autowired
    private McpConfig mcpConfig;
    
    @Test
    void testMcpConfigInjection() {
        assertNotNull(mcpConfig);
        assertNotNull(mcpConfig.getConnection());
        // Just check that the config is properly injected, not the specific values
        assertTrue(mcpConfig.getConnection().getTimeoutMs() > 0);
        assertTrue(mcpConfig.getConnection().getRetryCount() >= 0);
        assertTrue(mcpConfig.getConnection().getRetryIntervalMs() >= 0);
    }
}