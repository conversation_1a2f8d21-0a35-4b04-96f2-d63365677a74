package com.xiaozhi.dialogue.llm.tool.mcp;

import com.xiaozhi.communication.common.ChatSession;
import com.xiaozhi.dialogue.llm.tool.ToolsSessionHolder;
import com.xiaozhi.dialogue.llm.tool.mcp.thirdparty.ThirdPartyMcpManager;
import com.xiaozhi.entity.SysDevice;
import com.xiaozhi.entity.SysMcpEndpoint;
import com.xiaozhi.service.SysMcpEndpointService;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.ai.tool.function.FunctionToolCallback;
import org.springframework.ai.tool.definition.ToolDefinition;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * MCP集成测试
 * 测试从数据库查询MCP端点到工具注册的完整流程
 */
@ExtendWith(MockitoExtension.class)
class McpIntegrationTest {

    @Mock
    private SysMcpEndpointService mcpEndpointService;

    @Mock
    private ChatSession chatSession;

    @Mock
    private ToolsSessionHolder toolsSessionHolder;

    @Test
    void testCompleteWorkflow_FromDatabaseToToolRegistration() {
        // 模拟数据库中的MCP端点
        SysMcpEndpoint endpoint1 = new SysMcpEndpoint();
        endpoint1.setUrl("http://example.com/mcp1");
        endpoint1.setName("Test MCP 1");
        endpoint1.setToken("token1");
        endpoint1.setHeaders("{\"Custom-Header\": \"value1\"}");
        endpoint1.setIsEnabled(true);

        SysMcpEndpoint endpoint2 = new SysMcpEndpoint();
        endpoint2.setUrl("http://example.com/mcp2");
        endpoint2.setName("Test MCP 2");
        endpoint2.setToken("token2");
        endpoint2.setHeaders(null);
        endpoint2.setIsEnabled(true);

        List<SysMcpEndpoint> enabledEndpoints = Arrays.asList(endpoint1, endpoint2);

        // 模拟工具
        FunctionToolCallback tool1 = mock(FunctionToolCallback.class);
        FunctionToolCallback tool2 = mock(FunctionToolCallback.class);
        ToolDefinition toolDef1 = mock(ToolDefinition.class);
        ToolDefinition toolDef2 = mock(ToolDefinition.class);

        when(tool1.getToolDefinition()).thenReturn(toolDef1);
        when(tool2.getToolDefinition()).thenReturn(toolDef2);
        when(toolDef1.name()).thenReturn("mcp_file_read");
        when(toolDef2.name()).thenReturn("mcp_web_search");

        // 设置mock行为
        when(mcpEndpointService.getEnabledEndpoints()).thenReturn(enabledEndpoints);
        when(chatSession.getSessionId()).thenReturn("test-session-123");
        when(chatSession.getToolsSessionHolder()).thenReturn(toolsSessionHolder);
        when(chatSession.getSysDevice()).thenReturn(new SysDevice());

        // 创建ThirdPartyMcpManager的部分mock
        ThirdPartyMcpManager mcpManager = mock(ThirdPartyMcpManager.class);
        when(mcpManager.initializeEnabledEndpoints()).thenReturn(2);
        when(mcpManager.getAllTools()).thenReturn(Arrays.asList(tool1, tool2));

        // 创建McpToolRegistrationService
        McpToolRegistrationService registrationService = new McpToolRegistrationService();
        // 通过反射设置私有字段（在实际应用中会通过依赖注入）
        try {
            var mcpManagerField = McpToolRegistrationService.class.getDeclaredField("thirdPartyMcpManager");
            mcpManagerField.setAccessible(true);
            mcpManagerField.set(registrationService, mcpManager);
        } catch (Exception e) {
            fail("Failed to set up test: " + e.getMessage());
        }

        // 执行测试
        registrationService.initializeThirdPartyMcpToolsOnly(chatSession);

        // 验证结果
        verify(mcpManager, times(1)).initializeEnabledEndpoints();
        verify(mcpManager, times(1)).getAllTools();
        verify(toolsSessionHolder, times(2)).registerFunction(anyString(), any(FunctionToolCallback.class));
        verify(toolsSessionHolder).registerFunction("mcp_file_read", tool1);
        verify(toolsSessionHolder).registerFunction("mcp_web_search", tool2);
    }

    @Test
    void testWorkflow_NoEnabledEndpoints() {
        // 模拟没有启用的端点
        when(mcpEndpointService.getEnabledEndpoints()).thenReturn(Collections.emptyList());
        when(chatSession.getSessionId()).thenReturn("test-session-456");
        when(chatSession.getToolsSessionHolder()).thenReturn(toolsSessionHolder);

        ThirdPartyMcpManager mcpManager = mock(ThirdPartyMcpManager.class);
        when(mcpManager.initializeEnabledEndpoints()).thenReturn(0);
        when(mcpManager.getAllTools()).thenReturn(Collections.emptyList());

        McpToolRegistrationService registrationService = new McpToolRegistrationService();
        try {
            var mcpManagerField = McpToolRegistrationService.class.getDeclaredField("thirdPartyMcpManager");
            mcpManagerField.setAccessible(true);
            mcpManagerField.set(registrationService, mcpManager);
        } catch (Exception e) {
            fail("Failed to set up test: " + e.getMessage());
        }

        // 执行测试
        registrationService.initializeThirdPartyMcpToolsOnly(chatSession);

        // 验证结果
        verify(mcpManager, times(1)).initializeEnabledEndpoints();
        verify(mcpManager, times(1)).getAllTools();
        verify(toolsSessionHolder, never()).registerFunction(anyString(), any(FunctionToolCallback.class));
    }

    @Test
    void testWorkflow_PartialEndpointFailure() {
        // 模拟部分端点连接失败的情况
        when(chatSession.getSessionId()).thenReturn("test-session-789");
        when(chatSession.getToolsSessionHolder()).thenReturn(toolsSessionHolder);

        // 只有一个工具成功注册
        FunctionToolCallback successTool = mock(FunctionToolCallback.class);
        ToolDefinition toolDef = mock(ToolDefinition.class);
        when(successTool.getToolDefinition()).thenReturn(toolDef);
        when(toolDef.name()).thenReturn("mcp_successful_tool");

        ThirdPartyMcpManager mcpManager = mock(ThirdPartyMcpManager.class);
        when(mcpManager.initializeEnabledEndpoints()).thenReturn(1); // 只有1个成功
        when(mcpManager.getAllTools()).thenReturn(Arrays.asList(successTool));

        McpToolRegistrationService registrationService = new McpToolRegistrationService();
        try {
            var mcpManagerField = McpToolRegistrationService.class.getDeclaredField("thirdPartyMcpManager");
            mcpManagerField.setAccessible(true);
            mcpManagerField.set(registrationService, mcpManager);
        } catch (Exception e) {
            fail("Failed to set up test: " + e.getMessage());
        }

        // 执行测试
        registrationService.initializeThirdPartyMcpToolsOnly(chatSession);

        // 验证结果
        verify(mcpManager, times(1)).initializeEnabledEndpoints();
        verify(mcpManager, times(1)).getAllTools();
        verify(toolsSessionHolder, times(1)).registerFunction("mcp_successful_tool", successTool);
    }

    @Test
    void testMcpToolStats_Calculation() {
        // 创建模拟工具列表
        FunctionToolCallback mcpTool1 = mock(FunctionToolCallback.class);
        FunctionToolCallback mcpTool2 = mock(FunctionToolCallback.class);
        FunctionToolCallback regularTool = mock(FunctionToolCallback.class);

        ToolDefinition mcpToolDef1 = mock(ToolDefinition.class);
        ToolDefinition mcpToolDef2 = mock(ToolDefinition.class);
        ToolDefinition regularToolDef = mock(ToolDefinition.class);

        when(mcpTool1.getToolDefinition()).thenReturn(mcpToolDef1);
        when(mcpTool2.getToolDefinition()).thenReturn(mcpToolDef2);
        when(regularTool.getToolDefinition()).thenReturn(regularToolDef);

        when(mcpToolDef1.name()).thenReturn("mcp_tool_1");
        when(mcpToolDef2.name()).thenReturn("mcp_tool_2");
        when(regularToolDef.name()).thenReturn("regular_tool");

        List<org.springframework.ai.tool.ToolCallback> allTools = Arrays.asList(mcpTool1, mcpTool2, regularTool);
        when(chatSession.getToolCallbacks()).thenReturn(allTools);

        ThirdPartyMcpManager mcpManager = mock(ThirdPartyMcpManager.class);
        when(mcpManager.getAllTools()).thenReturn(Arrays.asList(mcpTool1, mcpTool2));

        McpToolRegistrationService registrationService = new McpToolRegistrationService();
        try {
            var mcpManagerField = McpToolRegistrationService.class.getDeclaredField("thirdPartyMcpManager");
            mcpManagerField.setAccessible(true);
            mcpManagerField.set(registrationService, mcpManager);
        } catch (Exception e) {
            fail("Failed to set up test: " + e.getMessage());
        }

        // 执行测试
        McpToolRegistrationService.McpToolStats stats = registrationService.getMcpToolStats(chatSession);

        // 验证结果
        assertEquals(3, stats.getTotalTools(), "总工具数应该是3");
        assertEquals(2, stats.getMcpTools(), "MCP工具数应该是2");
        assertEquals(2, stats.getThirdPartyMcpTools(), "第三方MCP工具数应该是2");

        // 验证toString方法
        String expectedString = "McpToolStats{totalTools=3, mcpTools=2, thirdPartyMcpTools=2}";
        assertEquals(expectedString, stats.toString());
    }

    @Test
    void testErrorHandling_ExceptionDuringInitialization() {
        // 模拟初始化过程中发生异常
        when(chatSession.getSessionId()).thenReturn("test-session-error");
        when(chatSession.getToolsSessionHolder()).thenReturn(toolsSessionHolder);

        ThirdPartyMcpManager mcpManager = mock(ThirdPartyMcpManager.class);
        when(mcpManager.initializeEnabledEndpoints()).thenThrow(new RuntimeException("Connection failed"));

        McpToolRegistrationService registrationService = new McpToolRegistrationService();
        try {
            var mcpManagerField = McpToolRegistrationService.class.getDeclaredField("thirdPartyMcpManager");
            mcpManagerField.setAccessible(true);
            mcpManagerField.set(registrationService, mcpManager);
        } catch (Exception e) {
            fail("Failed to set up test: " + e.getMessage());
        }

        // 执行测试 - 应该不抛出异常
        assertDoesNotThrow(() -> {
            registrationService.initializeThirdPartyMcpToolsOnly(chatSession);
        });

        // 验证异常被正确处理
        verify(mcpManager, times(1)).initializeEnabledEndpoints();
        verify(mcpManager, never()).getAllTools(); // 由于异常，不应该调用getAllTools
        verify(toolsSessionHolder, never()).registerFunction(anyString(), any(FunctionToolCallback.class));
    }
}
