package com.xiaozhi.config;

import com.xiaozhi.utils.CmsUtils;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.web.ServerProperties;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 服务器地址配置测试
 */
@SpringBootTest
@ActiveProfiles("dev")
public class ServerAddressConfigTest {

    @Autowired
    private ServerAddressConfig serverAddressConfig;

    @Autowired
    private CmsUtils cmsUtils;

    @Autowired
    private ServerProperties serverProperties;

    @Test
    public void testAddressInitialization() {
        // 测试地址是否正确初始化
        assertNotNull(serverAddressConfig.getWebsocketAddress());
        assertNotNull(serverAddressConfig.getOtaAddress());
        assertNotNull(serverAddressConfig.getServerAddress());

        System.out.println("WebSocket地址: " + serverAddressConfig.getWebsocketAddress());
        System.out.println("OTA地址: " + serverAddressConfig.getOtaAddress());
        System.out.println("服务器地址: " + serverAddressConfig.getServerAddress());

        // 验证地址格式
        assertTrue(serverAddressConfig.getWebsocketAddress().startsWith("ws://"));
        assertTrue(serverAddressConfig.getOtaAddress().startsWith("http://"));
        assertTrue(serverAddressConfig.getServerAddress().startsWith("http://"));

        // 验证端口是否正确
        String expectedPort = ":" + serverProperties.getPort();
        assertTrue(serverAddressConfig.getWebsocketAddress().contains(expectedPort));
        assertTrue(serverAddressConfig.getOtaAddress().contains(expectedPort));
        assertTrue(serverAddressConfig.getServerAddress().contains(expectedPort));
    }

    @Test
    public void testWebSocketPath() {
        // 验证WebSocket路径
        assertTrue(serverAddressConfig.getWebsocketAddress().contains("/ws/xiaozhi/v1/"));
    }

    @Test
    public void testOtaPath() {
        // 验证OTA路径
        assertTrue(serverAddressConfig.getOtaAddress().contains("/api/device/ota"));
    }

    @Test
    public void testServerIpConsistency() {
        // 验证使用的IP地址与CmsUtils获取的一致
        String serverIp = cmsUtils.getServerIp();
        assertTrue(serverAddressConfig.getWebsocketAddress().contains(serverIp));
        assertTrue(serverAddressConfig.getOtaAddress().contains(serverIp));
        assertTrue(serverAddressConfig.getServerAddress().contains(serverIp));
    }
}
