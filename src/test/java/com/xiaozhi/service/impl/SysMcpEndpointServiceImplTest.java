package com.xiaozhi.service.impl;

import com.xiaozhi.common.web.Resp;
import com.xiaozhi.config.McpConfig;
import com.xiaozhi.dao.SysMcpEndpointMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Test class for SysMcpEndpointServiceImpl
 */
class SysMcpEndpointServiceImplTest {
    
    @Mock
    private SysMcpEndpointMapper mcpEndpointMapper;
    
    @Mock
    private McpConfig mcpConfig;
    
    @InjectMocks
    private SysMcpEndpointServiceImpl mcpEndpointService;
    
    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }
    
    @Test
    void testGetAvailableToolsWithEmptyUrl() {
        // Test with empty URL
        Resp result = mcpEndpointService.getAvailableTools("", "token");
        
        assertNotNull(result);
        assertTrue(result instanceof Resp.Error);
        Resp.Error error = (Resp.Error) result;
        assertEquals(400, error.code());
        assertTrue(error.msg().contains("MCP URL不能为空"));
    }
    
    @Test
    void testGetAvailableToolsWithNullUrl() {
        // Test with null URL
        Resp result = mcpEndpointService.getAvailableTools(null, "token");
        
        assertNotNull(result);
        assertTrue(result instanceof Resp.Error);
        Resp.Error error = (Resp.Error) result;
        assertEquals(400, error.code());
        assertTrue(error.msg().contains("MCP URL不能为空"));
    }
    
    @Test
    void testExtractSseEndpointWithDefaultPath() {
        // Test extractSseEndpoint method through reflection or by testing the behavior
        // Since the method is private, we test it indirectly through getAvailableTools
        
        // This will fail to connect but should use the correct SSE endpoint
        Resp result = mcpEndpointService.getAvailableTools("https://example.com/mcp", "token");
        
        assertNotNull(result);
        assertTrue(result instanceof Resp.Error);
        Resp.Error error = (Resp.Error) result;
        assertEquals(500, error.code());
        assertTrue(error.msg().contains("获取工具失败"));
    }
    
    @Test
    void testExtractSseEndpointWithExistingPath() {
        // Test with URL that already contains SSE endpoint
        Resp result = mcpEndpointService.getAvailableTools("https://example.com/mcp/sse", "token");
        
        assertNotNull(result);
        assertTrue(result instanceof Resp.Error);
        Resp.Error error = (Resp.Error) result;
        assertEquals(500, error.code());
        assertTrue(error.msg().contains("获取工具失败"));
    }
    
    @Test
    void testTestEndpointConnectionWithEmptyUrl() {
        // Test testEndpointConnection with empty URL
        Resp result = mcpEndpointService.testEndpointConnection("", "token");
        
        assertNotNull(result);
        assertTrue(result instanceof Resp.Error);
        Resp.Error error = (Resp.Error) result;
        assertEquals(400, error.code());
        assertTrue(error.msg().contains("URL不能为空"));
    }
    
    @Test
    void testTestEndpointConnectionWithInvalidUrl() {
        // Test testEndpointConnection with invalid URL
        Resp result = mcpEndpointService.testEndpointConnection("https://invalid-url.local", "token");
        
        assertNotNull(result);
        assertTrue(result instanceof Resp.Error);
        Resp.Error error = (Resp.Error) result;
        assertEquals(500, error.code());
        assertTrue(error.msg().contains("连接测试失败"));
    }
}
