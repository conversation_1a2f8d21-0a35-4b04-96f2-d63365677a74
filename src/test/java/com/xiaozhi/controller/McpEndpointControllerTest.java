package com.xiaozhi.controller;

import com.xiaozhi.common.web.Resp;
import com.xiaozhi.config.McpConfig;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.when;

/**
 * Test class for McpEndpointController
 */
class McpEndpointControllerTest {

    @Mock
    private McpConfig mcpConfig;

    @InjectMocks
    private McpEndpointController mcpEndpointController;

    private MockMvc mockMvc;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        mockMvc = MockMvcBuilders.standaloneSetup(mcpEndpointController).build();

        // Mock McpConfig
        McpConfig.Connection connection = new McpConfig.Connection();
        connection.setTimeoutMs(10000);
        when(mcpConfig.getConnection()).thenReturn(connection);
    }
    
    @Test
    void testGetAvailableToolsWithEmptyUrl() {
        // Test with empty URL
        Resp result = mcpEndpointController.getAvailableTools("", null);

        assertNotNull(result);
        assertTrue(result instanceof Resp.Error);
        Resp.Error error = (Resp.Error) result;
        assertEquals(400, error.code());
        assertTrue(error.msg().contains("MCP URL不能为空"));
    }

    @Test
    void testGetAvailableToolsWithNullUrl() {
        // Test with null URL
        Resp result = mcpEndpointController.getAvailableTools(null, null);

        assertNotNull(result);
        assertTrue(result instanceof Resp.Error);
        Resp.Error error = (Resp.Error) result;
        assertEquals(400, error.code());
        assertTrue(error.msg().contains("MCP URL不能为空"));
    }

    @Test
    void testGetAvailableToolsWithInvalidUrl() {
        // Test with invalid URL that will cause connection failure
        String invalidUrl = "http://invalid-mcp-endpoint.local:12345";
        Map<String, String> headers = new HashMap<>();
        headers.put("Authorization", "Bearer test-token");

        Resp result = mcpEndpointController.getAvailableTools(invalidUrl, headers);

        assertNotNull(result);
        // Should be an error response due to connection failure
        if (result instanceof Resp.Error error) {
            assertEquals(500, error.code());
            assertTrue(error.msg().contains("获取MCP端点工具时发生错误") ||
                      error.msg().contains("无法连接到MCP端点"));
        } else {
            // If it's not an error, something unexpected happened
            fail("Expected Resp.Error but got: " + result.getClass().getSimpleName());
        }
    }
    
    @Test
    void testControllerInitialization() {
        // Test that the controller is properly initialized
        assertNotNull(mcpEndpointController);
    }
}
