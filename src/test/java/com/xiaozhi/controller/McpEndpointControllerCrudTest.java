package com.xiaozhi.controller;

import com.xiaozhi.common.web.Resp;
import com.xiaozhi.dto.McpEndpointDto;
import com.xiaozhi.entity.SysMcpEndpoint;
import com.xiaozhi.service.SysMcpEndpointService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;

/**
 * Test class for McpEndpointController CRUD operations
 */
class McpEndpointControllerCrudTest {
    
    @Mock
    private SysMcpEndpointService mcpEndpointService;
    
    @InjectMocks
    private McpEndpointController mcpEndpointController;
    
    private MockMvc mockMvc;
    
    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        mockMvc = MockMvcBuilders.standaloneSetup(mcpEndpointController).build();
    }
    
    @Test
    void testList() {
        // Mock service response
        Map<String, Object> mockData = new HashMap<>();
        mockData.put("records", new java.util.ArrayList<>());
        mockData.put("total", 0L);
        mockData.put("current", 1L);
        mockData.put("size", 10L);
        mockData.put("pages", 0L);
        
        when(mcpEndpointService.findPage(anyInt(), anyInt(), any(), any()))
                .thenReturn(Resp.succeed(mockData));
        
        Resp result = mcpEndpointController.list(1, 10, null, null);
        
        assertNotNull(result);
        assertTrue(result instanceof Resp.Result);
    }
    
    @Test
    void testGetById() {
        // Mock existing endpoint
        SysMcpEndpoint mockEndpoint = new SysMcpEndpoint();
        mockEndpoint.setId(1);
        mockEndpoint.setName("Test Endpoint");
        mockEndpoint.setUrl("https://test.com/mcp");
        mockEndpoint.setIsEnabled(true);

        when(mcpEndpointService.getById(1)).thenReturn(mockEndpoint);
        
        Resp result = mcpEndpointController.getById(1);
        
        assertNotNull(result);
        assertTrue(result instanceof Resp.Result);
    }
    
    @Test
    void testGetByIdNotFound() {
        when(mcpEndpointService.getById(999)).thenReturn(null);

        Resp result = mcpEndpointController.getById(999);

        assertNotNull(result);
        assertTrue(result instanceof Resp.Error);
        Resp.Error error = (Resp.Error) result;
        assertEquals(404, error.code());
        assertTrue(error.msg().contains("端点不存在"));
    }
    
    @Test
    void testAdd() {
        McpEndpointDto dto = new McpEndpointDto();
        dto.setName("New Endpoint");
        dto.setUrl("https://new.com/mcp");
        dto.setEnabled(true);
        
        when(mcpEndpointService.create(any(McpEndpointDto.class)))
                .thenReturn(Resp.succeed("添加成功"));
        
        Resp result = mcpEndpointController.add(dto);
        
        assertNotNull(result);
        assertTrue(result instanceof Resp.Result);
    }
    
    @Test
    void testUpdate() {
        McpEndpointDto dto = new McpEndpointDto();
        dto.setName("Updated Endpoint");
        dto.setUrl("https://updated.com/mcp");
        dto.setEnabled(false);
        
        when(mcpEndpointService.update(eq(1), any(McpEndpointDto.class)))
                .thenReturn(Resp.succeed("更新成功"));

        Resp result = mcpEndpointController.update(1, dto);
        
        assertNotNull(result);
        assertTrue(result instanceof Resp.Result);
    }
    
    @Test
    void testDelete() {
        when(mcpEndpointService.delete(1))
                .thenReturn(Resp.succeed("删除成功"));

        Resp result = mcpEndpointController.delete(1);

        assertNotNull(result);
        assertTrue(result instanceof Resp.Result);
    }

    @Test
    void testToggleStatus() {
        when(mcpEndpointService.toggleEndpointStatus(1, false))
                .thenReturn(Resp.succeed("状态更新成功"));

        Resp result = mcpEndpointController.toggleStatus(1, false);

        assertNotNull(result);
        assertTrue(result instanceof Resp.Result);
    }
    
    @Test
    void testTestConnection() {
        when(mcpEndpointService.testEndpointConnection("https://test.com/mcp", "token"))
                .thenReturn(Resp.succeed("连接成功"));
        
        Resp result = mcpEndpointController.testConnection("https://test.com/mcp", "token");
        
        assertNotNull(result);
        assertTrue(result instanceof Resp.Result);
    }
}
