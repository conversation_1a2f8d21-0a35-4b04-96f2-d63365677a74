package com.xiaozhi.communication.server.mqtt;

import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

/**
 * MQTT 配置测试
 */
public class MqttConfigTest {

    @Test
    public void testBrokerDefaultValues() {
        MqttConfig.Broker broker = new MqttConfig.Broker();
        
        assertEquals("localhost", broker.getHost());
        assertEquals(1883, broker.getPort());
        assertFalse(broker.isSsl());
        assertEquals("tcp://localhost:1883", broker.getUrl());
    }

    @Test
    public void testBrokerUrlParsing() {
        MqttConfig.Broker broker = new MqttConfig.Broker();
        
        // 测试 TCP URL 解析
        broker.setUrl("tcp://example.com:1883");
        assertEquals("example.com", broker.getHost());
        assertEquals(1883, broker.getPort());
        assertFalse(broker.isSsl());
        
        // 测试 SSL URL 解析
        broker.setUrl("ssl://secure.example.com:8883");
        assertEquals("secure.example.com", broker.getHost());
        assertEquals(8883, broker.getPort());
        assertTrue(broker.isSsl());
    }

    @Test
    public void testBrokerUrlGeneration() {
        MqttConfig.Broker broker = new MqttConfig.Broker();
        
        // 测试 TCP URL 生成
        broker.setHost("test.com");
        broker.setPort(1883);
        broker.setSsl(false);
        assertEquals("tcp://test.com:1883", broker.getUrl());
        
        // 测试 SSL URL 生成
        broker.setHost("secure.test.com");
        broker.setPort(8883);
        broker.setSsl(true);
        assertEquals("ssl://secure.test.com:8883", broker.getUrl());
    }
}
