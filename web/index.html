<!DOCTYPE html>
<html>

<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width,initial-scale=1.0">
  <link rel="stylesheet" href="https://unpkg.com/ant-design-vue@1.7.8/dist/antd.min.css">
  <link rel="stylesheet" href="https://unpkg.com/nprogress@0.2.0/nprogress.css">
  <link rel="stylesheet" href="https://unpkg.com/vue-virtual-scroller@1.0.10/dist/vue-virtual-scroller.css">
  <link rel="stylesheet" href="https://unpkg.com/v-charts@1.19.0/lib/style.min.css">
  <link rel="Shortcut Icon" href="/static/img/favicon.ico" type="image/x-icon">
  <title>Connect Ai-智能物联网管理平台</title>
</head>

<body>
  <div id="app"></div>
  <!-- built files will be auto injected -->
  <script src="https://unpkg.com/nprogress@0.2.0/nprogress.js"></script>
  <script src="https://unpkg.com/qs@6.9.4/dist/qs.js"></script>
  <script src="https://unpkg.com/vue@2.6.12/dist/vue.min.js"></script>
  <script src="https://unpkg.com/vue-router@3.4.9/dist/vue-router.min.js"></script>
  <script src="https://unpkg.com/vuex@3.6.0/dist/vuex.min.js"></script>
  <script src="https://unpkg.com/moment@2.18.1/min/moment-with-locales.min.js"></script>
  <script src="https://unpkg.com/ant-design-vue@1.7.8/dist/antd-with-locales.min.js"></script>
  <script src="https://unpkg.com/jsencrypt@3.0.0-rc.1/bin/jsencrypt.min.js"></script>
  <script src="https://unpkg.com/js-cookie@2.2.1/src/js.cookie.js"></script>
  <script src="https://unpkg.com/axios@0.21.0/dist/axios.min.js"></script>
  <script src="https://unpkg.com/clipboard@2.0.6/dist/clipboard.min.js"></script>
  <script src="https://unpkg.com/vue-virtual-scroller@1.0.10/dist/vue-virtual-scroller.umd.js"></script>
  <script src="https://unpkg.com/lazysizes@5.3.0/lazysizes.min.js"></script>
  <script src="https://unpkg.com/html2canvas@1.0.0-rc.7/dist/html2canvas.min.js"></script>
  <script src="https://unpkg.com/vue-cropper@0.5.5/dist/index.js"></script>
  <script src="https://unpkg.com/echarts/dist/echarts.min.js"></script>
  <script src="https://unpkg.com/v-charts@1.19.0/lib/index.min.js"></script>
</body>

</html>