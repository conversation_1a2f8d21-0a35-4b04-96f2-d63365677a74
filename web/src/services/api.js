export default {
  auth: {
    login: "/api/v1/auth/login",
  },
  user: {
    add: "/api/v1/users",
    query: "/api/v1/users",
    update: "/api/v1/users",
    sendEmailCaptcha: "/api/v1/users/email-captcha",
    checkCaptcha: "/api/v1/users/check-captcha",
    checkUser: "/api/v1/users/check-user",
  },
  device: {
    add: "/api/v1/devices",
    query: "/api/v1/devices",
    update: "/api/v1/devices",
    delete: "/api/v1/devices",
    export: "/api/v1/devices/export"
  },
  agent: {
    add: "/api/v1/agents",
    query: "/api/v1/agents",
    update: "/api/v1/agents",
    delete: "/api/v1/agents"
  },
  role: {
    add: "/api/v1/roles",
    query: "/api/v1/roles",
    update: "/api/v1/roles",
    testVoice: "/api/v1/roles/test-voice"
  },
  template: {
    query: "/api/v1/templates",
    add: "/api/v1/templates",
    update: "/api/v1/templates",
  },
  message: {
    query: "/api/v1/message/query",
    update: "/api/v1/message/update",
    delete: "/api/v1/message/delete",
    export: "/api/v1/message/export"
  },
  config: {
    add: "/api/v1/configs",
    query: "/api/v1/configs",
    update: "/api/v1/configs",
    getModels: "/api/v1/config/getModels"
  },
  upload: "/api/v1/file/upload"
};
