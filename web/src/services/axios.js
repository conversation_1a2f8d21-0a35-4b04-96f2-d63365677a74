import axios from "axios";
const qs = window.Qs;
import { message } from "ant-design-vue";
import { TokenManager, logout } from "@/utils/auth";

// 设置axios的基础URL，根据环境变量
axios.defaults.baseURL = process.env.BASE_API;
// 设置携带凭证
axios.defaults.withCredentials = true;

// 请求拦截器 - 添加 Authorization 头
axios.interceptors.request.use(
  config => {
    const token = TokenManager.getToken();
    if (token) {
      config.headers.Authorization = token;
    }
    return config;
  },
  error => {
    return Promise.reject(error);
  }
);

// 响应拦截器 - 处理认证错误
axios.interceptors.response.use(
  response => {
    // 检查响应数据中的认证错误
    if (response.data && (response.data.code === 401 || response.data.code === 403)) {
      message.error({
        content: "登录过期，请重新登录！",
        key: "auth-error",
        onClose: () => {
          logout();
        }
      });
    }
    return response;
  },
  error => {
    // 检查 HTTP 状态码的认证错误
    if (error.response && (error.response.status === 401 || error.response.status === 403)) {
      message.error({
        content: "登录过期，请重新登录！",
        key: "auth-error",
        onClose: () => {
          logout();
        }
      });
    }
    return Promise.reject(error);
  }
);

// 创建一个工具函数，用于处理静态资源URL
export const getResourceUrl = (path) => {
  if (!path) return '';

  if (path.startsWith('http://') || path.startsWith('https://')) {
    return path;
  }
  
  // 确保URL以/开头
  if (!path.startsWith('/')) {
    path = '/' + path;
  }
  
  // 开发环境下，需要使用完整的后端地址
  if (process.env.NODE_ENV === 'development') {
    // 开发环境下，我们需要指定后端地址
    // 如果BASE_API为空，则使用默认的localhost:8091
    const backendUrl = process.env.BACKEND_URL || 'http://localhost:8091';
    
    // 移除开头的斜杠，因为我们要将完整的URL传给组件
    if (path.startsWith('/')) {
      path = path.substring(1);
    }
    
    // 构建完整的URL
    return `${backendUrl}/${path}`;
  }
  
  // 生产环境下，直接使用相对路径，由Nginx代理处理
  return path;
};

function Rest() {}
Rest.prototype = {
  jsonPost(opts) {
    return new Promise((resolve, reject) => {
      axios({
        method: "post",
        url: opts.url,
        headers: {
          "Content-Type": "application/json;charset=UTF-8"
        },
        transformRequest: [
          function() {
            return JSON.stringify(opts.data);
          }
        ]
      })
        .then(res => {
          commonResponse(res.data, resolve);
        })
        .catch(e => {
          rejectResponse(e, reject);
        });
    });
  },
  post(opts) {
    return new Promise((resolve, reject) => {
      axios({
        method: "post",
        url: opts.url,
        headers: {
          "Content-Type": "application/x-www-form-urlencoded;charset=UTF-8"
        },
        transformRequest: [
          function() {
            return qs.stringify(opts.data);
          }
        ]
      })
        .then(res => {
          commonResponse(res.data, resolve);
        })
        .catch(e => {
          rejectResponse(e, reject);
        });
    });
  },
  get(opts) {
    return new Promise((resolve, reject) => {
      axios(opts.url, {
        params: opts.data
      })
        .then(res => {
          commonResponse(res.data, resolve);
        })
        .catch(e => {
          rejectResponse(e, reject);
        });
    });
  },
  delete(opts) {
    return new Promise((resolve, reject) => {
      axios({
        method: "delete",
        url: opts.url,
        params: opts.data
      })
        .then(res => {
          commonResponse(res.data, resolve);
        })
        .catch(e => {
          rejectResponse(e, reject);
        });
    });
  },
  put(opts) {
    return new Promise((resolve, reject) => {
      axios({
        method: "put",
        url: opts.url,
        headers: {
          "Content-Type": "application/json;charset=UTF-8"
        },
        data: opts.data
      })
        .then(res => {
          commonResponse(res.data, resolve);
        })
        .catch(e => {
          rejectResponse(e, reject);
        });
    });
  }
};
function commonResponse(data, resolve) {
  if (data.code === 401 || data.code === 403) {
    message.error({
      content: "登录过期，请重新登录！",
      key: "auth-error",
      onClose: () => {
        logout();
      }
    });
  } else {
    resolve(data);
  }
}

function rejectResponse(e, reject) {
  if (e.response && (e.response.status === 401 || e.response.status === 403)) {
    message.error({
      content: "登录过期，请重新登录！",
      key: "auth-error",
      onClose: () => {
        logout();
      }
    });
  } else {
    reject(e);
  }
}
export default new Rest();
