const TOKEN_KEY = 'access_token'
const USER_INFO_KEY = 'user_info'

/**
 * Token 管理工具 - 使用 localStorage
 */
export const TokenManager = {
  /**
   * 获取 Token
   */
  getToken() {
    return localStorage.getItem(TOKEN_KEY)
  },

  /**
   * 设置 Token
   * @param {string} token JWT Token
   */
  setToken(token) {
    try {
      localStorage.setItem(TOKEN_KEY, token)
      return true
    } catch (error) {
      console.error('设置 token 失败:', error)
      return false
    }
  },

  /**
   * 移除 Token
   */
  removeToken() {
    try {
      localStorage.removeItem(TOKEN_KEY)
      return true
    } catch (error) {
      console.error('移除 token 失败:', error)
      return false
    }
  },

  /**
   * 检查是否已登录
   */
  isLoggedIn() {
    return !!this.getToken()
  },

  /**
   * 获取用户信息
   */
  getUserInfo() {
    try {
      const userInfo = localStorage.getItem(USER_INFO_KEY)
      return userInfo ? JSON.parse(userInfo) : null
    } catch (error) {
      console.error('获取用户信息失败:', error)
      return null
    }
  },

  /**
   * 设置用户信息
   * @param {object} userInfo 用户信息
   */
  setUserInfo(userInfo) {
    try {
      localStorage.setItem(USER_INFO_KEY, JSON.stringify(userInfo))
      return true
    } catch (error) {
      console.error('设置用户信息失败:', error)
      return false
    }
  },

  /**
   * 移除用户信息
   */
  removeUserInfo() {
    try {
      localStorage.removeItem(USER_INFO_KEY)
      return true
    } catch (error) {
      console.error('移除用户信息失败:', error)
      return false
    }
  },

  /**
   * 清除所有认证信息
   */
  clearAuth() {
    this.removeToken()
    this.removeUserInfo()
  }
}

/**
 * 登出处理
 */
export const logout = () => {
  TokenManager.clearAuth()
  // 跳转到登录页
  window.location.href = process.env.BASE_URL || '/login'
}

export default TokenManager
